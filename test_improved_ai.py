#!/usr/bin/env python3
"""
Test script for improved AI prediction system
Tests IBM RXN with enhanced rate limiting and OpenAI fallback
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_improved_prediction_system():
    """Test the improved prediction system with IBM RXN priority."""
    print("🚀 Testing Improved AI Prediction System")
    print("=" * 60)
    
    try:
        from product_predictor import ProductPredictor
        
        # Initialize predictor
        print("🔧 Initializing ProductPredictor...")
        predictor = ProductPredictor()
        
        # Check what's available
        print(f"\n📊 System Status:")
        print(f"   IBM RXN API Key: {'✅' if predictor.ibm_rxn_api_key else '❌'}")
        print(f"   IBM RXN Wrapper: {'✅' if predictor.rxn_wrapper else '❌'}")
        print(f"   OpenAI API: {'✅' if predictor.ai_available else '❌'}")
        
        # Test cases
        test_cases = [
            {
                'name': 'Ethanol + Acetic Acid (Esterification)',
                'reactant_a': 'CCO',  # Ethanol
                'reactant_b': 'CC(=O)O',  # Acetic acid
                'expected_product': 'CC(=O)OCC'  # Ethyl acetate
            },
            {
                'name': 'Methanol + Formaldehyde',
                'reactant_a': 'CO',  # Methanol
                'reactant_b': 'C=O',  # Formaldehyde
                'expected_product': 'COC'  # Possible product
            }
        ]
        
        success_count = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'='*60}")
            print(f"🧪 Test Case {i}: {test_case['name']}")
            print(f"   Reactants: {test_case['reactant_a']} + {test_case['reactant_b']}")
            print(f"   Expected: {test_case['expected_product']}")
            print(f"{'='*60}")
            
            # Test the complete pipeline
            try:
                results = predictor.predict_and_validate(
                    test_case['reactant_a'], 
                    test_case['reactant_b'],
                    temperature=298.15,
                    use_ai=True,
                    ai_method="auto"
                )
                
                if results and 'predicted_products' in results:
                    products = results['predicted_products']
                    source = results.get('prediction_source', 'Unknown')
                    
                    print(f"\n📊 Results:")
                    print(f"   Prediction Source: {source}")
                    print(f"   Products Found: {len(products) if products else 0}")
                    
                    if products:
                        print(f"   Products:")
                        for j, product in enumerate(products, 1):
                            print(f"      {j}. {product}")
                        
                        # Check if expected product is found
                        if test_case['expected_product'] in products:
                            print(f"   ✅ Expected product found!")
                        else:
                            print(f"   ⚠️  Expected product not found, but got alternatives")
                        
                        success_count += 1
                        print(f"   🎉 Test PASSED")
                    else:
                        print(f"   ❌ No products predicted")
                        print(f"   🔍 Error: {results.get('error', 'Unknown error')}")
                else:
                    print(f"   ❌ Invalid results: {results}")
                    
            except Exception as e:
                print(f"   ❌ Test failed with error: {e}")
                import traceback
                print(f"   📝 Details: {traceback.format_exc()}")
        
        # Summary
        print(f"\n{'='*60}")
        print(f"📈 Test Summary")
        print(f"{'='*60}")
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {success_count}")
        print(f"   Success Rate: {(success_count/total_tests)*100:.1f}%")
        
        if success_count == total_tests:
            print(f"   🎉 ALL TESTS PASSED!")
        elif success_count > 0:
            print(f"   ⚠️  PARTIAL SUCCESS")
        else:
            print(f"   ❌ ALL TESTS FAILED")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Failed to initialize system: {e}")
        return False

def test_individual_methods():
    """Test individual prediction methods."""
    print(f"\n🔬 Testing Individual Methods")
    print("=" * 40)
    
    try:
        from product_predictor import ProductPredictor
        predictor = ProductPredictor()
        
        reactant_a = "CCO"  # Ethanol
        reactant_b = "CC(=O)O"  # Acetic acid
        
        # Test IBM RXN specifically
        if predictor.rxn_wrapper:
            print(f"\n📡 Testing IBM RXN directly...")
            try:
                rxn_products = predictor._predict_with_rxn(reactant_a, reactant_b, 298.15, "standard")
                if rxn_products:
                    print(f"   ✅ IBM RXN Success: {rxn_products}")
                else:
                    print(f"   ⚠️  IBM RXN: No products")
            except Exception as e:
                print(f"   ❌ IBM RXN Error: {e}")
        else:
            print(f"   ❌ IBM RXN not available")
        
        # Test OpenAI specifically
        if predictor.ai_available:
            print(f"\n🤖 Testing OpenAI GPT directly...")
            try:
                openai_products = predictor._predict_with_openai(reactant_a, reactant_b, 298.15, "standard")
                if openai_products:
                    print(f"   ✅ OpenAI Success: {openai_products}")
                else:
                    print(f"   ⚠️  OpenAI: No products")
            except Exception as e:
                print(f"   ❌ OpenAI Error: {e}")
        else:
            print(f"   ❌ OpenAI not available")
        
        # Test Rules
        print(f"\n🔧 Testing RDKit Rules...")
        try:
            rule_products = predictor.predict_products_rules(reactant_a, reactant_b)
            if rule_products:
                print(f"   ✅ Rules Success: {rule_products}")
            else:
                print(f"   ⚠️  Rules: No products")
        except Exception as e:
            print(f"   ❌ Rules Error: {e}")
        
    except Exception as e:
        print(f"❌ Individual method testing failed: {e}")

if __name__ == "__main__":
    print("🧪 ChemLab AI Prediction Test Suite")
    print("=" * 80)
    
    # Test the complete system
    system_success = test_improved_prediction_system()
    
    # Test individual methods
    test_individual_methods()
    
    print(f"\n{'='*80}")
    if system_success:
        print(f"🎉 OVERALL RESULT: SUCCESS - Your AI prediction system is working!")
        print(f"   Priority: IBM RXN → OpenAI GPT → RDKit Rules")
        print(f"   Rate limiting and error handling implemented")
    else:
        print(f"⚠️  OVERALL RESULT: NEEDS ATTENTION")
        print(f"   Check API keys and network connectivity")
    print(f"{'='*80}")
