xarray-2025.7.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
xarray-2025.7.1.dist-info/METADATA,sha256=FPzyHjbhwFHq2TbTm3DLUmvCk83QLUnnTah8DLckjtQ,12159
xarray-2025.7.1.dist-info/RECORD,,
xarray-2025.7.1.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
xarray-2025.7.1.dist-info/entry_points.txt,sha256=dyEyg5iirIWzJ4IkCMqkHNCIgzVKJbZ5icslVmN3bRY,72
xarray-2025.7.1.dist-info/licenses/LICENSE,sha256=Uz1zWiu8QOzOviABTDiiv2-xr9SIOY0sUIAGSWCZrxc,10268
xarray-2025.7.1.dist-info/top_level.txt,sha256=OGV8AqTgYtuaw6YV6tevWXEdDI5vHJiARQCJgRyT7co,7
xarray/__init__.py,sha256=44sApTWPnZGH264Bkx61w3hus5SKuEyqtObkgtweecg,3753
xarray/__pycache__/__init__.cpython-313.pyc,,
xarray/__pycache__/coders.cpython-313.pyc,,
xarray/__pycache__/conventions.cpython-313.pyc,,
xarray/__pycache__/convert.cpython-313.pyc,,
xarray/__pycache__/groupers.cpython-313.pyc,,
xarray/__pycache__/tutorial.cpython-313.pyc,,
xarray/__pycache__/typing.cpython-313.pyc,,
xarray/__pycache__/ufuncs.cpython-313.pyc,,
xarray/backends/__init__.py,sha256=Z_n7h5vVIKhRECj0eVY_SD-dl2Kle1Z73dPCwLpAr7M,1467
xarray/backends/__pycache__/__init__.cpython-313.pyc,,
xarray/backends/__pycache__/api.cpython-313.pyc,,
xarray/backends/__pycache__/chunks.cpython-313.pyc,,
xarray/backends/__pycache__/common.cpython-313.pyc,,
xarray/backends/__pycache__/file_manager.cpython-313.pyc,,
xarray/backends/__pycache__/h5netcdf_.cpython-313.pyc,,
xarray/backends/__pycache__/locks.cpython-313.pyc,,
xarray/backends/__pycache__/lru_cache.cpython-313.pyc,,
xarray/backends/__pycache__/memory.cpython-313.pyc,,
xarray/backends/__pycache__/netCDF4_.cpython-313.pyc,,
xarray/backends/__pycache__/netcdf3.cpython-313.pyc,,
xarray/backends/__pycache__/plugins.cpython-313.pyc,,
xarray/backends/__pycache__/pydap_.cpython-313.pyc,,
xarray/backends/__pycache__/scipy_.cpython-313.pyc,,
xarray/backends/__pycache__/store.cpython-313.pyc,,
xarray/backends/__pycache__/zarr.cpython-313.pyc,,
xarray/backends/api.py,sha256=QhDkGEZGC49p2Fw4aRipUkAuKYkyN2Hzd0MbsrkJFRU,92889
xarray/backends/chunks.py,sha256=IurtFACkgFKTgabVbfp-NjcUCpXvL9vMWtdCqOiv3mU,11173
xarray/backends/common.py,sha256=wocJDYWPpCn7R7xddrxJWh49YriE_exkaXQiLp6MftU,24476
xarray/backends/file_manager.py,sha256=BFflxvTg-ye9ahTAaetmGWeZjvIR5ENzWtWu_2xTHec,12506
xarray/backends/h5netcdf_.py,sha256=gOKsfHRLAc68Csd-tE6P-IVgfo6PEdlIC9oCmWxL6Lo,20224
xarray/backends/locks.py,sha256=K6YUvCNHjbqAuWty8GgMI5d6MJzrYSlukRPE0FmSRbY,7664
xarray/backends/lru_cache.py,sha256=qZsFldc1MZduWnkJuLNe5IMhxyTbi4kThrLUwOIpePI,3661
xarray/backends/memory.py,sha256=SL4JeacOXji7Yua6f7yEf6j1oeuOvnhcNWczg4ThJVY,1525
xarray/backends/netCDF4_.py,sha256=JRedsH5mxevEPSObfqxwZ9dYdDYA346RVzp3fox7xXQ,26629
xarray/backends/netcdf3.py,sha256=ht_Ye3v1W3Yuyka9XyEuuDWGV_1dP1Aod9gEbzrrTA0,5728
xarray/backends/plugins.py,sha256=aRxqabb1yDZfw1A8-Xa1jsoIo6R8LAH8BkwseBEse7w,8263
xarray/backends/pydap_.py,sha256=SaWPwEwDyqmDtK9P6OefXQew5xX6GHDP8_o7sWizE7k,12688
xarray/backends/scipy_.py,sha256=4peYXd5bANAtWhB21O_spVtb_dW0lzQML4u8O2p0uaI,11465
xarray/backends/store.py,sha256=Klqg6ZRvLEqMLVJUljJxyz1mxTJXzu2DfUYkcg357E8,2467
xarray/backends/zarr.py,sha256=Ki4d23s8SlqSqjwgXyYBhoiFm3ywZvvXdmbHTX_RQtc,70145
xarray/coders.py,sha256=Jc2V-IIlsrV1P4U5f3rR79YnqUldVjUhpWqTK3zoWdc,211
xarray/coding/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/coding/__pycache__/__init__.cpython-313.pyc,,
xarray/coding/__pycache__/calendar_ops.cpython-313.pyc,,
xarray/coding/__pycache__/cftime_offsets.cpython-313.pyc,,
xarray/coding/__pycache__/cftimeindex.cpython-313.pyc,,
xarray/coding/__pycache__/common.cpython-313.pyc,,
xarray/coding/__pycache__/frequencies.cpython-313.pyc,,
xarray/coding/__pycache__/strings.cpython-313.pyc,,
xarray/coding/__pycache__/times.cpython-313.pyc,,
xarray/coding/__pycache__/variables.cpython-313.pyc,,
xarray/coding/calendar_ops.py,sha256=_-VoGFK8rRPorpCEmjMG9s-3oIPmzcblrTRF6mPrt04,16146
xarray/coding/cftime_offsets.py,sha256=KN6rBh53Z4RL7Y0IuMbS8J8_9pChCUvw2tAREqgY8xw,62797
xarray/coding/cftimeindex.py,sha256=I05A4thloLrGJhhZlfutuesunQdb0QJ59pw-Dr-3kBg,30503
xarray/coding/common.py,sha256=A2K7_PIkoA-A7cIcdaYL-PFOsHqa9iB_U0siCfxjbZ0,4960
xarray/coding/frequencies.py,sha256=BALN4xBlOwFhsOB1qu3HByg-UKWXu3vcdfgayKLfi-Q,9380
xarray/coding/strings.py,sha256=zeKsnMtpWjS12gK1lcPjxGdKx-QcEG1ORn9-YG7_Mb4,10657
xarray/coding/times.py,sha256=WzVnoxKVxVeO1BN9EOs69pKbaDNeKKCmDsvVLKGEpac,60153
xarray/coding/variables.py,sha256=KbuENqAVdbbBVFCGr58v5aEnP6KTCfu55Irjcr1QHiE,26121
xarray/compat/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/compat/__pycache__/__init__.cpython-313.pyc,,
xarray/compat/__pycache__/array_api_compat.cpython-313.pyc,,
xarray/compat/__pycache__/dask_array_compat.cpython-313.pyc,,
xarray/compat/__pycache__/dask_array_ops.cpython-313.pyc,,
xarray/compat/__pycache__/npcompat.cpython-313.pyc,,
xarray/compat/__pycache__/pdcompat.cpython-313.pyc,,
xarray/compat/__pycache__/toolzcompat.cpython-313.pyc,,
xarray/compat/array_api_compat.py,sha256=qY05hKdaA1x9cjWiAR-qwAM6q0I5lW1naKdNMqqIOQI,2551
xarray/compat/dask_array_compat.py,sha256=1AqD4vqxVk6RsvzmiTk5nl3MFhIbDApei_xH1cxWU2E,1050
xarray/compat/dask_array_ops.py,sha256=abJmuWrvttUtGkquyDNxQQ3FXWJ5Dz9u9EHrdJR6SwQ,4626
xarray/compat/npcompat.py,sha256=dxpuJsxnn2V04cOWHZ13AuWSmvcBMm1SLaCnwjtwVkc,3254
xarray/compat/pdcompat.py,sha256=_S8N8_SVzASjcZ7R0YWUyyay7SAu2aOH8jyPg8hX2Bw,3474
xarray/compat/toolzcompat.py,sha256=6c1eeQ8slOuuzmYMqHNkKeVyIy_d16m0h2XFOEaja8U,2318
xarray/computation/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/computation/__pycache__/__init__.cpython-313.pyc,,
xarray/computation/__pycache__/apply_ufunc.cpython-313.pyc,,
xarray/computation/__pycache__/arithmetic.cpython-313.pyc,,
xarray/computation/__pycache__/computation.cpython-313.pyc,,
xarray/computation/__pycache__/fit.cpython-313.pyc,,
xarray/computation/__pycache__/nanops.cpython-313.pyc,,
xarray/computation/__pycache__/ops.cpython-313.pyc,,
xarray/computation/__pycache__/rolling.cpython-313.pyc,,
xarray/computation/__pycache__/rolling_exp.cpython-313.pyc,,
xarray/computation/__pycache__/weighted.cpython-313.pyc,,
xarray/computation/apply_ufunc.py,sha256=ByQrOEG3-IdliIMaCIzg3RrPQLSjuRDNapMoAStUVr8,47505
xarray/computation/arithmetic.py,sha256=3gc13nEh4LSV9WhbNx_udayGm7FUZ4KqajWD2u6oeDc,4329
xarray/computation/computation.py,sha256=2NrIIeLxvmONIjlNFO_Ly7we4ZeHatlNH4E_2u7GN4Q,31296
xarray/computation/fit.py,sha256=4PVTWVSyDu2NQAKZ2SSRa8iI42_3_rtyJflnJurIiHY,19898
xarray/computation/nanops.py,sha256=_RSX0L2pogV9JroSNCXOJRwyH3KOxspeSD1SLEpOZB0,5631
xarray/computation/ops.py,sha256=FJJAXwtMgGG-Qk8hFJzmdAwSpJhavTlvHj1-P4thNQk,9310
xarray/computation/rolling.py,sha256=N2wd4p36ZpzqLdb4wi8Fpvrw2TSFDWhC9ZCClgKBAXA,50059
xarray/computation/rolling_exp.py,sha256=UW0dDibxCkr1j3YRyeyOdiQ74ebnJRxC-ulwN1IIA1g,9389
xarray/computation/weighted.py,sha256=HLP1q7HZMwH8aPSmSJhr9abUFsvXbFCj63korWXimWE,19738
xarray/conventions.py,sha256=RzIqMHekP5SCkktS7CL9PuOq7Lf6A3KLkaVoHtJvZyk,31818
xarray/convert.py,sha256=LBEYaz4MVSGcAKJJywVRZ6yI_lj30D0GlVYljnKiKlA,6643
xarray/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/core/__pycache__/__init__.cpython-313.pyc,,
xarray/core/__pycache__/_aggregations.cpython-313.pyc,,
xarray/core/__pycache__/_typed_ops.cpython-313.pyc,,
xarray/core/__pycache__/accessor_dt.cpython-313.pyc,,
xarray/core/__pycache__/accessor_str.cpython-313.pyc,,
xarray/core/__pycache__/common.cpython-313.pyc,,
xarray/core/__pycache__/coordinate_transform.cpython-313.pyc,,
xarray/core/__pycache__/coordinates.cpython-313.pyc,,
xarray/core/__pycache__/dataarray.cpython-313.pyc,,
xarray/core/__pycache__/dataset.cpython-313.pyc,,
xarray/core/__pycache__/dataset_utils.cpython-313.pyc,,
xarray/core/__pycache__/dataset_variables.cpython-313.pyc,,
xarray/core/__pycache__/datatree.cpython-313.pyc,,
xarray/core/__pycache__/datatree_io.cpython-313.pyc,,
xarray/core/__pycache__/datatree_mapping.cpython-313.pyc,,
xarray/core/__pycache__/datatree_render.cpython-313.pyc,,
xarray/core/__pycache__/dtypes.cpython-313.pyc,,
xarray/core/__pycache__/duck_array_ops.cpython-313.pyc,,
xarray/core/__pycache__/extension_array.cpython-313.pyc,,
xarray/core/__pycache__/extensions.cpython-313.pyc,,
xarray/core/__pycache__/formatting.cpython-313.pyc,,
xarray/core/__pycache__/formatting_html.cpython-313.pyc,,
xarray/core/__pycache__/groupby.cpython-313.pyc,,
xarray/core/__pycache__/indexes.cpython-313.pyc,,
xarray/core/__pycache__/indexing.cpython-313.pyc,,
xarray/core/__pycache__/missing.cpython-313.pyc,,
xarray/core/__pycache__/nputils.cpython-313.pyc,,
xarray/core/__pycache__/options.cpython-313.pyc,,
xarray/core/__pycache__/parallel.cpython-313.pyc,,
xarray/core/__pycache__/resample.cpython-313.pyc,,
xarray/core/__pycache__/resample_cftime.cpython-313.pyc,,
xarray/core/__pycache__/treenode.cpython-313.pyc,,
xarray/core/__pycache__/types.cpython-313.pyc,,
xarray/core/__pycache__/utils.cpython-313.pyc,,
xarray/core/__pycache__/variable.cpython-313.pyc,,
xarray/core/_aggregations.py,sha256=kdmCWw8Ytz_SKNPMNOe_mA7LzuO4WRI3lX7qHtmqO3Y,338072
xarray/core/_typed_ops.py,sha256=d9BN4yS6vRHxy5S5oXzdigX99eUs9zgFG8h2g2W019M,54365
xarray/core/accessor_dt.py,sha256=nr7Foa83SdJ9fB1pLLwJwyIMu43b2s3_2iqqV8DoDw4,23406
xarray/core/accessor_str.py,sha256=azWR6OQ6bH-mYgomrsUrrQnh4vrmrL3bqiU9uHGnpco,99665
xarray/core/common.py,sha256=cyohUvu_YdOgrKgNpsI6pO-3PYBdiEkdEYlsifYCOiQ,75273
xarray/core/coordinate_transform.py,sha256=uCpcdijaqNtoKghAUU7XYStTVvYTCDd9AeLOEnKxkl4,3448
xarray/core/coordinates.py,sha256=3uGSBJx23jy3uAdp2dfnSC7EQuZ1jxtkRqoz6AaXf2I,43080
xarray/core/dataarray.py,sha256=1lmVf67y65_EqxmG3xLaXajzVNiNc-hDTEHJJnYBPHc,290879
xarray/core/dataset.py,sha256=NeCFYq1Tu3szZvcW-GMYWStPEiB0ZWPNW8MCLct0VK4,402624
xarray/core/dataset_utils.py,sha256=vXJ9lEIUCY5-RNkjzSDtGLkFn2YOW_ZmusNw0BZofcs,2662
xarray/core/dataset_variables.py,sha256=92hiAo_bkNoibpaLvupAClebu04dhXL2FtVXarTIYog,2054
xarray/core/datatree.py,sha256=fAiWGWIcFRrBR3LTcFEOgNUbmsCWHFtI2M997eqCNP0,82429
xarray/core/datatree_io.py,sha256=StcQu-cZd67CvMhzpsuVKXYxTCq6wWkhWu8tChwqiNc,4340
xarray/core/datatree_mapping.py,sha256=U2U9Nj-uFPVmSQxkORzn7FwakTuKXQyAxGLGJ1iFVIg,8024
xarray/core/datatree_render.py,sha256=-k9SqFrQ0vONh_pJTR7g7TbOE2tXKCGMLLYcPb0ieWc,9472
xarray/core/dtypes.py,sha256=BmatgetqMltR9Dx6yb5SL6wbjkaIWESE6hum4PMJKwU,8323
xarray/core/duck_array_ops.py,sha256=kt2psDn8-Lh7Xpenwo82fSGdVzhqnwyhT6uJIDYSI8M,30680
xarray/core/extension_array.py,sha256=CpSV2KAunO0Mh38r1ZsodH7UhdyZ24Uu1sWyhkLF_7c,7196
xarray/core/extensions.py,sha256=B71HYyLLFF7NKWiD6cHHM2mHx_6WyObeFN7ckOLGoB0,4013
xarray/core/formatting.py,sha256=qQ34UNf3OSSt0xJtTv-0U0MKpYn5tLt9W6VHqqSnyCw,40422
xarray/core/formatting_html.py,sha256=Zam5GF1c12ySSByebf3wEuzjUaJesGKb14kFJGWttcs,15086
xarray/core/groupby.py,sha256=774PhADlTGftEkiow9KUEqTRpHxljV9cLUnRGbVtaEY,68259
xarray/core/indexes.py,sha256=BGahlRF9vrLG94svzLZu3tUNqhRmmPvi-zS5_FF7Tq0,79729
xarray/core/indexing.py,sha256=YD4CesX6K9oeZInJmL0nur3q9BRCiIlA5POBLMv6Bg4,75497
xarray/core/missing.py,sha256=zTfKPdP0z7x2HIi7TACWNnJEGoLUrwDI40koheozmuQ,28401
xarray/core/nputils.py,sha256=eeMK5ki7LwNKhrgEkihgNDzfEqjEM8nqqTI52WrzfUI,11105
xarray/core/options.py,sha256=fCagA212mBIqaPHO10GdHJVQjKxYYNIOxf1JyrIe1dE,12309
xarray/core/parallel.py,sha256=XgJyrCUZcRcSZz-t5Fmp7SPaGj5aukWlKVCJjj5ifdY,24814
xarray/core/resample.py,sha256=5IlNxGyvSCVyHKVSNoXfLMZd1fSiQCrmIXNB0Fjkx-A,18181
xarray/core/resample_cftime.py,sha256=23-aJ4nk85HBwBmG4og24Xp34BnuyPq6Z5boTJxHL0c,18662
xarray/core/treenode.py,sha256=CRpSsFlhuPWxQmBty-c0Azu1xX-H8Oh73UskVXp6Hjk,29521
xarray/core/types.py,sha256=Hb9kWNRotj3EQuv-VqltRvfjRZSGu3EdGsMHnoucYCM,11587
xarray/core/utils.py,sha256=7DL6j-IGdqJO_NB3_r9S8fz3gYedvX0YlYtDSfYeBnc,39879
xarray/core/variable.py,sha256=Et-3O_BoyoJhWnozoIbiaYJwG7Dlh7LGERFNnrDJVmI,113711
xarray/groupers.py,sha256=Lng24yLSo9Yg9ThecclTLNFLRrfJJjNHw79o8JcGaH4,35750
xarray/indexes/__init__.py,sha256=rx5RrvHrVqxjmMbTl_3fWAzddLKpHrk5scwkUOeY4_I,588
xarray/indexes/__pycache__/__init__.cpython-313.pyc,,
xarray/indexes/__pycache__/nd_point_index.cpython-313.pyc,,
xarray/indexes/__pycache__/range_index.cpython-313.pyc,,
xarray/indexes/nd_point_index.py,sha256=noJ7eEgnaFqMRlCVo6YDqDeaFI_F4RopJdnV6gocrFA,13330
xarray/indexes/range_index.py,sha256=6sdZ3gAZ_6Vpy07UN3v_g5ygaSErCHh7_iLUjfoMmIk,13207
xarray/namedarray/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/namedarray/__pycache__/__init__.cpython-313.pyc,,
xarray/namedarray/__pycache__/_aggregations.cpython-313.pyc,,
xarray/namedarray/__pycache__/_array_api.cpython-313.pyc,,
xarray/namedarray/__pycache__/_typing.cpython-313.pyc,,
xarray/namedarray/__pycache__/core.cpython-313.pyc,,
xarray/namedarray/__pycache__/daskmanager.cpython-313.pyc,,
xarray/namedarray/__pycache__/dtypes.cpython-313.pyc,,
xarray/namedarray/__pycache__/parallelcompat.cpython-313.pyc,,
xarray/namedarray/__pycache__/pycompat.cpython-313.pyc,,
xarray/namedarray/__pycache__/utils.cpython-313.pyc,,
xarray/namedarray/_aggregations.py,sha256=kNGdylEXlhDkI_mEiI8JQMQTXJ3y0eRZMAj6PNBs3oM,30297
xarray/namedarray/_array_api.py,sha256=_dEJDQwQ2DmRamGJQM_a3IiAW7jdBBEExQLNiFblfu8,5971
xarray/namedarray/_typing.py,sha256=5mRzr6NG7_yPhqJul5g7nDjUsobF1uMxN8SOwMZ9p1A,8075
xarray/namedarray/core.py,sha256=mFiDOUEqRvnck6ywJeoDxWF08rnxWbg26ErOUP3nMXo,40021
xarray/namedarray/daskmanager.py,sha256=csLzegkd_ywWwSwyDV4CZyO3iu5NZlvDhKhiYbxtiqs,7989
xarray/namedarray/dtypes.py,sha256=rraglcNdVNfKYHmFr-SbZEChngzdkhETg7tLF6_o5cA,5577
xarray/namedarray/parallelcompat.py,sha256=RLGeGqzf21KfbeCZ_0zRHuSni9IegpFCGTmnANyI1ew,27069
xarray/namedarray/pycompat.py,sha256=c0nt5J7_zT9izMAm_NA8EeYghU7znadRO_S-MQpBDsk,5080
xarray/namedarray/utils.py,sha256=7WTXf00Lv6a7sUZ872daTA6nFrSBGQjNbMFjiMqfrAY,6804
xarray/plot/__init__.py,sha256=LXM-35Wj3p7LP4TqxtgtGPfq5f4NEvGOKpvEwXfgGrM,574
xarray/plot/__pycache__/__init__.cpython-313.pyc,,
xarray/plot/__pycache__/accessor.cpython-313.pyc,,
xarray/plot/__pycache__/dataarray_plot.cpython-313.pyc,,
xarray/plot/__pycache__/dataset_plot.cpython-313.pyc,,
xarray/plot/__pycache__/facetgrid.cpython-313.pyc,,
xarray/plot/__pycache__/utils.cpython-313.pyc,,
xarray/plot/accessor.py,sha256=xJjyCEpnMB2Q-gccqBiWcxm-10qSIrKxeiA_O-HEfWI,43213
xarray/plot/dataarray_plot.py,sha256=Z-CPq2qePzsj9oKLexZhje3FCG2X0h-cChOXk7MIs9Y,86396
xarray/plot/dataset_plot.py,sha256=cA5VUqRyj1rov09HM5lSaPu0-9psn0yMNAEoF5s7Ohw,30870
xarray/plot/facetgrid.py,sha256=hCAqquKumFF7IWulj_wlYnZjvoSISJbPjSlHl3RVFEE,37980
xarray/plot/utils.py,sha256=0TVyvUlozpLLBcG6jlfcoAHBWa5T4R0COfIZBNo0wb4,60793
xarray/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/__pycache__/__init__.cpython-313.pyc,,
xarray/static/css/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/css/__pycache__/__init__.cpython-313.pyc,,
xarray/static/css/style.css,sha256=yVaAD-7Z0mKcTl0iAJZ99jpPE2Poo7j2r9hece_3dGU,8046
xarray/static/html/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/html/__pycache__/__init__.cpython-313.pyc,,
xarray/static/html/icons-svg-inline.html,sha256=t-ChbtS1Gv8uZxc31DCJS8SuXDsLGUHoKgwv8zu6j2M,1343
xarray/structure/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/structure/__pycache__/__init__.cpython-313.pyc,,
xarray/structure/__pycache__/alignment.cpython-313.pyc,,
xarray/structure/__pycache__/chunks.cpython-313.pyc,,
xarray/structure/__pycache__/combine.cpython-313.pyc,,
xarray/structure/__pycache__/concat.cpython-313.pyc,,
xarray/structure/__pycache__/merge.cpython-313.pyc,,
xarray/structure/alignment.py,sha256=Cf6l2QNsbWFz5uBOpExGmCwlQ_fKwskd4W3asSGTaB0,43782
xarray/structure/chunks.py,sha256=gQTqN84u1HDA9EKcNFM1wKPtT5SABfHggI2KUdhdVvg,8189
xarray/structure/combine.py,sha256=HfU6lcVTVt2005sN3gUsG7OQ5bsvpzaJkXc2F9RTCME,37865
xarray/structure/concat.py,sha256=SMjLewMLU6PzcGW7SBOgJPWJE6mIVZ17l8CxLx799mk,30888
xarray/structure/merge.py,sha256=Cvzu1q1jy2qVmU2ziUd5DGKpcRK8FvSwk-BMzKINshg,39820
xarray/testing/__init__.py,sha256=G3FtrJ8WAw9atekFS99viR0-sOpeQ8Udd_KJoFEzYQo,621
xarray/testing/__pycache__/__init__.cpython-313.pyc,,
xarray/testing/__pycache__/assertions.cpython-313.pyc,,
xarray/testing/__pycache__/strategies.cpython-313.pyc,,
xarray/testing/assertions.py,sha256=o3hfELekKWeK40TaTyVEAMlSVwtvqpGrfxBiw0gElqU,18053
xarray/testing/strategies.py,sha256=UWx6wmlkbMUoVTer9sPXgr8f-K2NRc6188C2nGtcDxI,17538
xarray/tests/__init__.py,sha256=h89cIf5Vv52Dh_lO1l2KB1TtBprHJ3JF_iZGa_ltlcs,14047
xarray/tests/__pycache__/__init__.cpython-313.pyc,,
xarray/tests/__pycache__/arrays.cpython-313.pyc,,
xarray/tests/__pycache__/conftest.cpython-313.pyc,,
xarray/tests/__pycache__/indexes.cpython-313.pyc,,
xarray/tests/__pycache__/namespace.cpython-313.pyc,,
xarray/tests/__pycache__/test_accessor_dt.cpython-313.pyc,,
xarray/tests/__pycache__/test_accessor_str.cpython-313.pyc,,
xarray/tests/__pycache__/test_array_api.cpython-313.pyc,,
xarray/tests/__pycache__/test_assertions.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends_api.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends_chunks.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends_common.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends_datatree.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends_file_manager.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends_locks.cpython-313.pyc,,
xarray/tests/__pycache__/test_backends_lru_cache.cpython-313.pyc,,
xarray/tests/__pycache__/test_calendar_ops.cpython-313.pyc,,
xarray/tests/__pycache__/test_cftime_offsets.cpython-313.pyc,,
xarray/tests/__pycache__/test_cftimeindex.cpython-313.pyc,,
xarray/tests/__pycache__/test_cftimeindex_resample.cpython-313.pyc,,
xarray/tests/__pycache__/test_coarsen.cpython-313.pyc,,
xarray/tests/__pycache__/test_coding.cpython-313.pyc,,
xarray/tests/__pycache__/test_coding_strings.cpython-313.pyc,,
xarray/tests/__pycache__/test_coding_times.cpython-313.pyc,,
xarray/tests/__pycache__/test_combine.cpython-313.pyc,,
xarray/tests/__pycache__/test_computation.cpython-313.pyc,,
xarray/tests/__pycache__/test_concat.cpython-313.pyc,,
xarray/tests/__pycache__/test_conventions.cpython-313.pyc,,
xarray/tests/__pycache__/test_coordinate_transform.cpython-313.pyc,,
xarray/tests/__pycache__/test_coordinates.cpython-313.pyc,,
xarray/tests/__pycache__/test_cupy.cpython-313.pyc,,
xarray/tests/__pycache__/test_dask.cpython-313.pyc,,
xarray/tests/__pycache__/test_dataarray.cpython-313.pyc,,
xarray/tests/__pycache__/test_dataset.cpython-313.pyc,,
xarray/tests/__pycache__/test_datatree.cpython-313.pyc,,
xarray/tests/__pycache__/test_datatree_mapping.cpython-313.pyc,,
xarray/tests/__pycache__/test_deprecation_helpers.cpython-313.pyc,,
xarray/tests/__pycache__/test_distributed.cpython-313.pyc,,
xarray/tests/__pycache__/test_dtypes.cpython-313.pyc,,
xarray/tests/__pycache__/test_duck_array_ops.cpython-313.pyc,,
xarray/tests/__pycache__/test_duck_array_wrapping.cpython-313.pyc,,
xarray/tests/__pycache__/test_error_messages.cpython-313.pyc,,
xarray/tests/__pycache__/test_extensions.cpython-313.pyc,,
xarray/tests/__pycache__/test_formatting.cpython-313.pyc,,
xarray/tests/__pycache__/test_formatting_html.cpython-313.pyc,,
xarray/tests/__pycache__/test_groupby.cpython-313.pyc,,
xarray/tests/__pycache__/test_hashable.cpython-313.pyc,,
xarray/tests/__pycache__/test_indexes.cpython-313.pyc,,
xarray/tests/__pycache__/test_indexing.cpython-313.pyc,,
xarray/tests/__pycache__/test_interp.cpython-313.pyc,,
xarray/tests/__pycache__/test_merge.cpython-313.pyc,,
xarray/tests/__pycache__/test_missing.cpython-313.pyc,,
xarray/tests/__pycache__/test_namedarray.cpython-313.pyc,,
xarray/tests/__pycache__/test_nd_point_index.cpython-313.pyc,,
xarray/tests/__pycache__/test_nputils.cpython-313.pyc,,
xarray/tests/__pycache__/test_options.cpython-313.pyc,,
xarray/tests/__pycache__/test_pandas_to_xarray.cpython-313.pyc,,
xarray/tests/__pycache__/test_parallelcompat.cpython-313.pyc,,
xarray/tests/__pycache__/test_plot.cpython-313.pyc,,
xarray/tests/__pycache__/test_plugins.cpython-313.pyc,,
xarray/tests/__pycache__/test_print_versions.cpython-313.pyc,,
xarray/tests/__pycache__/test_range_index.cpython-313.pyc,,
xarray/tests/__pycache__/test_rolling.cpython-313.pyc,,
xarray/tests/__pycache__/test_sparse.cpython-313.pyc,,
xarray/tests/__pycache__/test_strategies.cpython-313.pyc,,
xarray/tests/__pycache__/test_treenode.cpython-313.pyc,,
xarray/tests/__pycache__/test_tutorial.cpython-313.pyc,,
xarray/tests/__pycache__/test_typed_ops.cpython-313.pyc,,
xarray/tests/__pycache__/test_ufuncs.cpython-313.pyc,,
xarray/tests/__pycache__/test_units.cpython-313.pyc,,
xarray/tests/__pycache__/test_utils.cpython-313.pyc,,
xarray/tests/__pycache__/test_variable.cpython-313.pyc,,
xarray/tests/__pycache__/test_weighted.cpython-313.pyc,,
xarray/tests/arrays.py,sha256=Y40bjyJLuq7zL8SGJJMETBXT2A4H8ZuWzpc02kqc1go,6304
xarray/tests/conftest.py,sha256=W0I2T3yF1ZK5k5H1e55iHNBd9GKvNcpxkimyEjPIopQ,6647
xarray/tests/data/bears.nc,sha256=912tQ5fHIS-VDTBe3UplQi2rdRcreMSQ0tIdCOg9FRI,1184
xarray/tests/data/example.grib,sha256=kCvzClBl8_r_G5Nhzpd4Zn-haB_rdNND1PF6rkmKsm0,5232
xarray/tests/data/example.ict,sha256=h53gvFYHQyb-9NOAulIsVYUOwKy5xZZxhNHXAg7L8mE,783
xarray/tests/data/example.uamiv,sha256=a3-OlPxWaY6iaOkLPzTVrYwE3mZDF7zvh-6CK8d5q3o,608
xarray/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
xarray/tests/data/example_1.nc.gz,sha256=2I3vFd3bUqecY6G0EpIrMMkRLrow03SUQXYRC6UjJh4,470
xarray/tests/indexes.py,sha256=1l1n1OP6T0cLz7_mnJaP_90zWcY97lpqv5ypNRV1mRs,2409
xarray/tests/namespace.py,sha256=YVBa9JnmQjx68yOeNa479Q3jn1DxheQrLd_upF6jp8w,161
xarray/tests/test_accessor_dt.py,sha256=qmfSCcePgQ1TxUh4gXgXUQTYOOJ6eV8GMeh8DzCaatY,22958
xarray/tests/test_accessor_str.py,sha256=otB27hAk-NF-myUgCOBEVq7Nt0vq1tmTdDzsGo-B3X8,121829
xarray/tests/test_array_api.py,sha256=K6gQnhoseWun82n4GbiWdpcsf9MD5maOfWgH2j9sEEY,4557
xarray/tests/test_assertions.py,sha256=iRnk7mlA0N4SNeuio6-PdJYTug5KZ9y9kBdggFj0mUE,6568
xarray/tests/test_backends.py,sha256=iDehAvcZaHQeXup7ODMJww8hbHbhAfK-KzSadPYnt6g,276375
xarray/tests/test_backends_api.py,sha256=rz0pFh37MjkjHqlKI6KT34BhjUcQc2W5pzQcG6aQWgc,8701
xarray/tests/test_backends_chunks.py,sha256=fvVT6JmEZylhE1LYuLTqbVWgZKymInj7qPAacfYKQUE,3124
xarray/tests/test_backends_common.py,sha256=aYs9PJ9hwWlU44Uo6_kO-4YABzgMEyB8F2JcFcS_cNs,1604
xarray/tests/test_backends_datatree.py,sha256=VAPFDayLK9rcrWKarMcAXfs7VuVHZ5ViCOutxHAJ6rc,37640
xarray/tests/test_backends_file_manager.py,sha256=bQDs6d7hz26wU7knz0aGXlfDdMlDwdU_goCfPCiWMIE,7188
xarray/tests/test_backends_locks.py,sha256=RYLmZIBFvQgmxIB8jFrOdw4GYzwFDd_gqvz5lUiZ_sI,366
xarray/tests/test_backends_lru_cache.py,sha256=N2DoiDR8nFwvmB2VeDbzVeS55XBvPYOqWfUkRNWnnN0,2310
xarray/tests/test_calendar_ops.py,sha256=KwEiZiW-EsK-VjBKOoSRCPCLxnL1Ov4ggb03J6-MDFI,10328
xarray/tests/test_cftime_offsets.py,sha256=yt282zXehGYVKq6mxrX_YE2Ihh2yEx5GtbsPWEAn3TA,54977
xarray/tests/test_cftimeindex.py,sha256=PRyHLCXQtwo_P_nY_ZRy1zuqSp0UcdDJ8UqLo1sFgF0,45450
xarray/tests/test_cftimeindex_resample.py,sha256=aG5qD7pPZ1lDSZ8QLR34lZzXOxmHPOUss7k48nwvFoA,7438
xarray/tests/test_coarsen.py,sha256=KmMgVAIsZi1sXhk_cucMU5yfJQigFOwditsOdfNnoG8,11821
xarray/tests/test_coding.py,sha256=kOycHMFOmqC8ktYRLf0xSH0DqVFBNf9jLF5yod_xPc8,5174
xarray/tests/test_coding_strings.py,sha256=3ctI3Yl_MeKoTilnvhd1OLo-Fe3Uxb2TyI7wWbXAZzQ,9480
xarray/tests/test_coding_times.py,sha256=64WGdqBXI6KZ6AOgk-nlczGVe8hbMINocSQ6iJWQ2_c,80099
xarray/tests/test_combine.py,sha256=ZVt60NQuskfGeKbgtuYbNhYxHXMHwNt1CR2zEvk5pWI,45036
xarray/tests/test_computation.py,sha256=oxHLowj0qTYL8PT0yJTJaY0815k6dIi9Y-WuSs2LHiM,87904
xarray/tests/test_concat.py,sha256=7SLwDphqArYVx1U8kLVXw1qVz2zIxMiQLOWG_2g-8oc,52466
xarray/tests/test_conventions.py,sha256=mcd7JPJ7o4pzavC2Ek-2nzeJJKVuF4BvygUUIv14h-g,26127
xarray/tests/test_coordinate_transform.py,sha256=gWtsxuf1wuLipVJCs2XWeXBGQVMIHPrHvdxfzVsvXck,8358
xarray/tests/test_coordinates.py,sha256=n9MrUyl3SsPtVLFh7w1pOk6EkEaCbdzLqlwgjZlvM2I,7846
xarray/tests/test_cupy.py,sha256=DVQOX2JEO8VLeKRRU78UPnUqoC-8damScHevr6V1sWM,1681
xarray/tests/test_dask.py,sha256=4Tmu2yHaFpA06G_FK5dWIJaZhC__lqEH6CPvrhoqK70,64812
xarray/tests/test_dataarray.py,sha256=Ez1SfWrrdFvJmFVjIxRhDFt7DcROWqqv8ds7GMrIrso,265250
xarray/tests/test_dataarray_typing.yml,sha256=b5simF4xNQRum8k60xw2NeiwFl8Jt53VjO9gfahC1Pk,8202
xarray/tests/test_dataset.py,sha256=FScaiq-i0YpnuUwMXh0QlJut6UN74T3XqA8e5mBrXvM,292997
xarray/tests/test_dataset_typing.yml,sha256=gRhZkvcZxS9m_-Lj-4BNFR7npt-uAaOtGuo3izM1AVE,8054
xarray/tests/test_datatree.py,sha256=qH8J4tDZdhxd7MaxVVMRTZZDBwTPBEcA802wnQ76vuo,88048
xarray/tests/test_datatree_mapping.py,sha256=2wiipu8sAyVxzBxpkqqCngZ5NrecUQHXZ-BkP4iNC2c,9628
xarray/tests/test_datatree_typing.yml,sha256=U0Vzv32efa6MFFTDbcO3TpuT1MR9yBj2rr-kRHjZ5eg,8074
xarray/tests/test_deprecation_helpers.py,sha256=WpCB8gzmLqP1tKWsQhJPOeMXe0l4709yLPU5lOCitWU,4630
xarray/tests/test_distributed.py,sha256=DgrLg3s4ukORtt27Ki-qudkqjfeLRNTIc5XzmsALrZU,10293
xarray/tests/test_dtypes.py,sha256=KzBPliSASs2Rq5DN8Vg-sTkW8SuEPcTZ4Pv4NChps5k,6107
xarray/tests/test_duck_array_ops.py,sha256=8buvyGuxpR_AgVvdJn_R7p-1_t-YLEGzbLKOVgqwLl8,38063
xarray/tests/test_duck_array_wrapping.py,sha256=m4Rc2J0EanGO6riNwhyGDqMG-rBv4fhU8XJFTyL_JDo,17880
xarray/tests/test_error_messages.py,sha256=SnOaA-Q7Ul8E_dsvsXtxhz21E4wutq3jMZC_jaZI4fI,511
xarray/tests/test_extensions.py,sha256=aGdibBCJjf21CMm3ayZ_Xma_Ki5djO_RbrvVv-lRQzQ,3014
xarray/tests/test_formatting.py,sha256=uAOhDbByfjCxgxUXBUCsz_4Ezi7vVSUE5PClfoR3tks,40752
xarray/tests/test_formatting_html.py,sha256=ZRG31XQmdgXPk4icdBXvQKSkXZFWyouEdLjcJye76qI,14868
xarray/tests/test_groupby.py,sha256=i-rRSlDdT7erkzkjyh5SNyRfk5g4exMT9yaZq-h9OLE,129261
xarray/tests/test_hashable.py,sha256=pV1092cRkKmNGK0l4-bpte7KxOohNBWgUh7WyvVTayc,1114
xarray/tests/test_indexes.py,sha256=JPS2FbM6NXcqqcnDQS_wCi66lbPmoibqc2YPY4x11fg,28578
xarray/tests/test_indexing.py,sha256=KZbD91fQdXh9brQD9ONKgGcL6xGecp5z4pxjA16ia9Y,37060
xarray/tests/test_interp.py,sha256=pSrDCm-tjyyUhH7tK3JDD-XR9r5ODT0nEoWKvx9KxrY,37366
xarray/tests/test_merge.py,sha256=EoY-XvhA5Kvg2xophnbdXkjIl2F-0g4gR4a8CArvmsE,19822
xarray/tests/test_missing.py,sha256=FPz_tctfpi3cpAxpgr2OVzrYLOizd16eW-MMScmiBtc,25789
xarray/tests/test_namedarray.py,sha256=yo0Vlw83h3N6k8R6i6k086hZ0HlGbLNwjAK5VJwxoxA,22182
xarray/tests/test_nd_point_index.py,sha256=FAkCDUsRQlloCz5aYQJyFmnKsSVxM6OB5OxFbiARc7E,6243
xarray/tests/test_nputils.py,sha256=KAes7xQZSusV-ReKD0eJISkel1AWTeKf5q3lkQwtUtA,987
xarray/tests/test_options.py,sha256=C2TdXfLFQSdKGXlf9wDbfIWszbCj3CchZeE1Ec7t3SY,7647
xarray/tests/test_pandas_to_xarray.py,sha256=9ddETK6itZAUFi0V_3NmnFa2563TXwUtKp_cYfLvMLM,8263
xarray/tests/test_parallelcompat.py,sha256=-aKT38C_P3hU7H1Rs5f7BxZFjJBU6VaKHsLnQ89oQwY,8605
xarray/tests/test_plot.py,sha256=5mG-3j0pui6bXeD_H-D_wsZHaCXaLZ-xEkLa739B8kE,129819
xarray/tests/test_plugins.py,sha256=heQwBc87mSQ00Duw8Yr_nQOSpYCHTlcn4PPc0nloSJ8,9931
xarray/tests/test_print_versions.py,sha256=TV6fQHajYW71VLvQRpemQQv8trMlFYrXRXwLTFRi7YM,200
xarray/tests/test_range_index.py,sha256=AOwLokv2356_wdXoQG2namwnyRYCif7fEjYB_GE3zZM,9044
xarray/tests/test_rolling.py,sha256=ZJhINaJfYGIcqfmxzlHWeACVuffA2V2T-idpvK79DGs,36421
xarray/tests/test_sparse.py,sha256=w8Br2Clt9LdbqMa4l_uwgTyMy8cNsVa2IKGivFc7hfQ,29278
xarray/tests/test_strategies.py,sha256=iJadYmtdmp5wC-bHNHavD7hyRoShi8I9uST5SgtAUJs,10237
xarray/tests/test_treenode.py,sha256=W33CHHSxt0coi-8rsqBtzPRZlHb3m0x0c-ljmsQsW38,15358
xarray/tests/test_tutorial.py,sha256=GvdVmc2MFLN3K8LrYDUlBseUU2hoCpfduxyYdBPRcqA,1659
xarray/tests/test_typed_ops.py,sha256=yrNOVMY2FMklODRRbeCRVeCEbjBACLl6bpDuYLKkGnM,6334
xarray/tests/test_ufuncs.py,sha256=7PINlplpYo6CiBbcTQGkttt9EkVc4kJM7Ju3wR0Zgjc,8803
xarray/tests/test_units.py,sha256=GMpDz5hY5AgWG208DK-1ys2kIdHIGo3_TJ7hh-DQdlk,189159
xarray/tests/test_utils.py,sha256=yl2_kov2BLRTVmYFyiXVeRh7mwQTEvVr-AnA7m236f4,12175
xarray/tests/test_variable.py,sha256=pSCVGOW6sbAv6rbgaxPbkm1KPF0d1u05btDDAR1HemY,117037
xarray/tests/test_weighted.py,sha256=LYWKZgTHIwoQKMzBHVJHON1790Oxhw9P9fds_j0WOMg,24321
xarray/tutorial.py,sha256=adFwzhX8oPEkt4SU6DzVqk9Km6TsoCP_a6UWfNOAwfE,12871
xarray/typing.py,sha256=0V8zJ578K10KfNZK4D2OE84m5jmsJ6rp2CFXa63YgP8,554
xarray/ufuncs.py,sha256=65-LiPUx3kEaNE6Xb1uGBI1T9qGxjlLHxmp_Dx2VcK0,8877
xarray/util/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/util/__pycache__/__init__.cpython-313.pyc,,
xarray/util/__pycache__/deprecation_helpers.cpython-313.pyc,,
xarray/util/__pycache__/generate_aggregations.cpython-313.pyc,,
xarray/util/__pycache__/generate_ops.cpython-313.pyc,,
xarray/util/__pycache__/print_versions.cpython-313.pyc,,
xarray/util/deprecation_helpers.py,sha256=AN0JAbol5Oed3jHSGAjfSnvVdqlFnc8D_kLxiwqhVPY,5485
xarray/util/generate_aggregations.py,sha256=3EtuVq2ZtcuijKls0v6PSdVIiPPckvu-Lda00LxvSDc,22798
xarray/util/generate_ops.py,sha256=V4ECbmFAjp0AhAh14KUDXREeX6Rfi2QaDoM8jwhaN_c,10476
xarray/util/print_versions.py,sha256=Pt4S4_G3nnjn4OINS83muPqVhiSMvW5KB-aaY-yBox4,5113
