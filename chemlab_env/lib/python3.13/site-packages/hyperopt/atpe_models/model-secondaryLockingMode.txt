tree
version=v2
num_class=2
num_tree_per_iteration=2
label_index=0
max_feature_idx=118
objective=multiclass num_class:2
feature_names=all_correlation_best_percentile25_ratio all_correlation_best_percentile50_ratio all_correlation_best_percentile75_ratio all_correlation_kurtosis all_correlation_percentile5_percentile25_ratio all_correlation_skew all_correlation_stddev_best_ratio all_correlation_stddev_median_ratio all_loss_best_percentile25_ratio all_loss_best_percentile50_ratio all_loss_best_percentile75_ratio all_loss_kurtosis all_loss_percentile5_percentile25_ratio all_loss_skew all_loss_stddev_best_ratio all_loss_stddev_median_ratio log10_cardinality recent_10_correlation_best_percentile25_ratio recent_10_correlation_best_percentile50_ratio recent_10_correlation_best_percentile75_ratio recent_10_correlation_kurtosis recent_10_correlation_percentile5_percentile25_ratio recent_10_correlation_skew recent_10_correlation_stddev_best_ratio recent_10_correlation_stddev_median_ratio recent_10_loss_best_percentile25_ratio recent_10_loss_best_percentile50_ratio recent_10_loss_best_percentile75_ratio recent_10_loss_kurtosis recent_10_loss_percentile5_percentile25_ratio recent_10_loss_skew recent_10_loss_stddev_best_ratio recent_10_loss_stddev_median_ratio recent_15%_correlation_best_percentile25_ratio recent_15%_correlation_best_percentile50_ratio recent_15%_correlation_best_percentile75_ratio recent_15%_correlation_kurtosis recent_15%_correlation_percentile5_percentile25_ratio recent_15%_correlation_skew recent_15%_correlation_stddev_best_ratio recent_15%_correlation_stddev_median_ratio recent_15%_loss_best_percentile25_ratio recent_15%_loss_best_percentile50_ratio recent_15%_loss_best_percentile75_ratio recent_15%_loss_kurtosis recent_15%_loss_percentile5_percentile25_ratio recent_15%_loss_skew recent_15%_loss_stddev_best_ratio recent_15%_loss_stddev_median_ratio recent_25_correlation_best_percentile25_ratio recent_25_correlation_best_percentile50_ratio recent_25_correlation_best_percentile75_ratio recent_25_correlation_kurtosis recent_25_correlation_percentile5_percentile25_ratio recent_25_correlation_skew recent_25_correlation_stddev_best_ratio recent_25_correlation_stddev_median_ratio recent_25_loss_best_percentile25_ratio recent_25_loss_best_percentile50_ratio recent_25_loss_best_percentile75_ratio recent_25_loss_kurtosis recent_25_loss_percentile5_percentile25_ratio recent_25_loss_skew recent_25_loss_stddev_best_ratio recent_25_loss_stddev_median_ratio top_10%_correlation_best_percentile25_ratio top_10%_correlation_best_percentile50_ratio top_10%_correlation_best_percentile75_ratio top_10%_correlation_kurtosis top_10%_correlation_percentile5_percentile25_ratio top_10%_correlation_skew top_10%_correlation_stddev_best_ratio top_10%_correlation_stddev_median_ratio top_10%_loss_best_percentile25_ratio top_10%_loss_best_percentile50_ratio top_10%_loss_best_percentile75_ratio top_10%_loss_kurtosis top_10%_loss_percentile5_percentile25_ratio top_10%_loss_skew top_10%_loss_stddev_best_ratio top_10%_loss_stddev_median_ratio top_20%_correlation_best_percentile25_ratio top_20%_correlation_best_percentile50_ratio top_20%_correlation_best_percentile75_ratio top_20%_correlation_kurtosis top_20%_correlation_percentile5_percentile25_ratio top_20%_correlation_skew top_20%_correlation_stddev_best_ratio top_20%_correlation_stddev_median_ratio top_20%_loss_best_percentile25_ratio top_20%_loss_best_percentile50_ratio top_20%_loss_best_percentile75_ratio top_20%_loss_kurtosis top_20%_loss_percentile5_percentile25_ratio top_20%_loss_skew top_20%_loss_stddev_best_ratio top_20%_loss_stddev_median_ratio top_30%_correlation_best_percentile25_ratio top_30%_correlation_best_percentile50_ratio top_30%_correlation_best_percentile75_ratio top_30%_correlation_kurtosis top_30%_correlation_percentile5_percentile25_ratio top_30%_correlation_skew top_30%_correlation_stddev_best_ratio top_30%_correlation_stddev_median_ratio top_30%_loss_best_percentile25_ratio top_30%_loss_best_percentile50_ratio top_30%_loss_best_percentile75_ratio top_30%_loss_kurtosis top_30%_loss_percentile5_percentile25_ratio top_30%_loss_skew top_30%_loss_stddev_best_ratio top_30%_loss_stddev_median_ratio resultFilteringMode_age resultFilteringMode_loss_rank resultFilteringMode_none resultFilteringMode_random secondaryProbabilityMode_correlation secondaryProbabilityMode_fixed
feature_infos=[-9.0793999999999997:106.40000000000001] [-6.1543999999999999:907.00999999999999] [-4.2420999999999998:4891.5] [-4.0632000000000001:42.643000000000001] [-11.725:138.13999999999999] [-12.686:13.845000000000001] [-15.976000000000001:11.5] [-5.9271000000000003:1428.2] [-92019:46103] [-76234:115450] [-15884:122060] [-2.3426:70.739999999999995] [-81702:47027] [-8.2988:19.844000000000001] [-537830:1444500] [-78181:1104700] [-4.9066000000000001:5.7077999999999998] [-4.2309000000000001:1690.3] [-2.4914999999999998:363.81999999999999] [-2.2536:422.11000000000001] [-2.5891000000000002:34.893999999999998] [-5.0574000000000003:1705.5] [-9.4838000000000005:11.569000000000001] [-15.170999999999999:8.0465] [-2.4289999999999998:353.56] [-15750:17733] [-24245:129280] [-29342:94593] [-1.7628999999999999:2.0127000000000002] [-12687:18631] [-6.274:1.8354999999999999] [-378090:1398600] [-339220:1724900] [-4.3403:8764.7999999999993] [-2.6692:4616.3000000000002] [-2.3054999999999999:8748.2999999999993] [-2.5735000000000001:33.960000000000001] [-5.2473000000000001:9928.8999999999996] [-11.114000000000001:11.558] [-14.411:7.4763999999999999] [-2.6036000000000001:4298.5] [-25805:15193] [-71151:398300] [-232340:102990] [-1.6065:49.856000000000002] [-23318:13028] [-7.5063000000000004:12.785] [-779600:1158500] [-3854900:1321600] [-4.9035000000000002:9369.6000000000004] [-3.0550999999999999:3670.5] [-2.6476999999999999:4790.8000000000002] [-2.8029999999999999:42.148000000000003] [-6.0103999999999997:9025.7999999999993] [-10.73:12.619999999999999] [-13.798999999999999:7.9203999999999999] [-2.8595999999999999:3237.1999999999998] [-110400:32742] [-54569:50533] [-25601:21684] [-1.9413:3.7551999999999999] [-121220:7508.6000000000004] [-9.4586000000000006:3.0712000000000002] [-777050:1005600] [-4907700:2416500] [-3.5528:6421.6000000000004] [-2.3841000000000001:3859.3000000000002] [-1.8844000000000001:2265.0999999999999] [-2.2686999999999999:28.308] [-4.1837:6406.6000000000004] [-13.297000000000001:10.804] [-13.407999999999999:6.319] [-2.3730000000000002:3342.0999999999999] [-22397:39663] [-37882:31527] [-54135:285190] [-3.0364:128.66999999999999] [-21922:40673] [-24.335000000000001:19.193000000000001] [-555170:366600] [-27388:44588] [-3.9630000000000001:22230] [-2.3159000000000001:13472] [-1.9112:3249.8000000000002] [-2.4296000000000002:30.291] [-4.7637999999999998:13939] [-14.045:11.15] [-14.471:7.2553999999999998] [-2.3260000000000001:9986.7999999999993] [-80788:9101.8999999999996] [-96031:28600] [-78856:27997] [-3.8111000000000002:275.38999999999999] [-38099:9126.2999999999993] [-36.381999999999998:30.282] [-440240:271940] [-31583:88057] [-4.4935999999999998:76437] [-2.3953000000000002:17238] [-2.0042:18072] [-2.6255999999999999:30.370000000000001] [-5.4583000000000004:76103] [-11.244999999999999:10.606999999999999] [-14.869999999999999:7.6917] [-2.3576999999999999:17510] [-63240:23966] [-27011:25286] [-308980:655530] [-4.2102000000000004:378.39999999999998] [-85724:27764] [-41.186:32.061] [-557080:1121200] [-23140:353280] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1]
tree_sizes=2651 2600

Tree=0
num_leaves=31
num_cat=0
split_feature=117 69 75 44 92 76 76 91 7 44 44 10 71 43 44 80 91 29 66 35 35 40 5 10 63 13 13 13 41 43
split_gain=889.122 737.344 232.12 212.89 143.227 116.457 70.3686 63.8356 58.3009 58.1901 39.3703 38.9642 33.9838 29.2346 27.9575 23.6714 21.5796 19.5014 18.7167 18.0664 17.8571 17.5634 17.4667 17.1134 22.4171 21.3843 15.4059 13.7394 13.4744 13.5818
threshold=1.0000000180025095e-35 -1.8729499999999997 0.15360500000000002 -0.72895499999999991 -3.8109999999999995 -1.5781499999999997 -3.0362999999999993 0.47705000000000003 -0.3998549999999999 -0.36117999999999995 -0.36117999999999995 -0.86797999999999986 1.7775500000000004 -0.59635999999999989 0.038387500000000005 -1.03535 1.0080500000000001 3.5127500000000005 -1.5126999999999997 -1.4100499999999998 -1.6427499999999997 -1.5144499999999999 2.5897500000000004 1.3038500000000004 -1.2400499999999999 -0.10846999999999998 0.75078500000000015 -0.30890999999999996 4.3303000000000011 1.9100500000000002
decision_type=2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2
left_child=1 3 9 7 -2 21 -6 15 23 12 16 14 18 -5 -10 -1 -7 -9 -3 27 26 -4 -13 24 -8 -26 -12 -11 29 -18
right_child=4 2 5 13 6 10 8 17 11 19 20 22 -14 -15 -16 -17 28 -19 -20 -21 -22 -23 -24 -25 25 -27 -28 -29 -30 -31
leaf_value=-0.012148962148962149 -0.007113064203783704 -0.028217244195543112 -0.013056478405315614 -0.030163977783655118 -0.010834840265220013 -0.026945198913564467 -0.024809599080327635 -0.0081763449213439511 -0.016723793201942304 -0.029542612113632308 -0.024472226381194791 -0.016074259491030456 -0.02389352148813342 -0.022087952026167548 -0.023184792770333439 -0.018983152827918171 -0.021841755319148937 -0.029039301310043671 -0.032725354235755198 -0.035837526959022287 -0.030739903981926012 -0.019976560835286598 -0.011776192902020718 -0.017777280858676209 -0.017796439706326061 -0.02230370237882311 -0.029400563007120385 -0.033701052016364696 -0.031530782029950084 -0.013386524822695037
leaf_count=4095 6713 8571 3010 3781 6636 6259 6959 10298 7002 5597 6679 23970 4677 5503 6418 6648 6016 229 9951 13910 10623 4693 5889 8944 9943 11182 6039 13688 601 1128
internal_value=0 -0.521455 -0.579464 -0.32886 -0.341292 -0.503774 -0.356659 -0.25087 -0.368224 -0.639926 -0.536431 -0.332979 -0.585586 -0.50754 -0.396274 -0.327562 -0.477149 -0.172604 -0.612785 -0.677903 -0.571998 -0.34545 -0.304531 -0.40942 -0.426577 -0.403645 -0.536248 -0.649883 -0.427243 -0.410134
internal_count=225652 131996 101442 30554 93656 45048 86943 21270 80307 56394 37345 43279 23199 9284 13420 10743 14004 10527 18522 33195 23341 7703 29859 37028 28084 21125 12718 19285 7745 7144
shrinkage=0.05


Tree=1
num_leaves=31
num_cat=0
split_feature=118 69 75 44 92 76 76 91 7 44 44 10 71 44 16 41 80 29 115 66 35 35 40 42 107 115 106 13 10 63
split_gain=889.122 737.344 232.12 212.89 143.227 116.457 70.3686 63.8356 58.3009 58.1901 39.3703 38.9642 33.9838 27.9575 26.7561 25.2295 23.6714 19.5014 19.2975 18.7167 18.0664 17.8571 17.5634 16.738 16.576 15.6223 15.6191 15.4059 15.3757 18.7306
threshold=1.0000000180025095e-35 -1.8729499999999997 0.15360500000000002 -0.72895499999999991 -3.8109999999999995 -1.5781499999999997 -3.0362999999999993 0.47705000000000003 -0.3998549999999999 -0.36117999999999995 -0.36117999999999995 -0.86797999999999986 1.7775500000000004 0.038387500000000005 -0.27687999999999996 0.78084500000000012 -1.03535 3.5127500000000005 1.0000000180025095e-35 -1.5126999999999997 -1.4100499999999998 -1.6427499999999997 -1.5144499999999999 1.8514000000000002 0.67773500000000009 1.0000000180025095e-35 2.7717000000000005 0.75078500000000015 1.2524500000000003 -1.1610499999999997
decision_type=2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2
left_child=4 3 9 7 -1 22 -6 16 18 12 14 13 19 -10 24 -5 -2 -9 28 -3 -11 27 -4 -16 -7 -13 -25 -12 29 -8
right_child=1 2 5 15 6 10 8 17 11 20 21 25 -14 -15 23 -17 -18 -19 -20 -21 -22 -23 -24 26 -26 -27 -28 -29 -30 -31
leaf_value=0.007113064203783704 0.012148962148962149 0.028217244195543112 0.013056478405315614 0.028970588235294123 0.010834840265220013 0.025808314087759816 0.026391475274156841 0.0081763449213439511 0.016723793201942304 0.032494166450609284 0.024472226381194791 0.016378875088357066 0.02389352148813342 0.023184792770333439 0.027845263919016636 0.021595932802829357 0.018983152827918171 0.029039301310043671 0.01813627254509018 0.032725354235755198 0.035837526959022287 0.030739903981926012 0.019976560835286598 0.016482213438735179 0.01747988580327018 0.012956331443350245 0.036192468619246861 0.029400563007120385 0.018549032154855226 0.021284364261168386
leaf_count=6713 4095 8571 3010 4760 6636 1732 4833 10298 7002 19285 6679 19806 4677 6418 6915 4524 6648 229 11976 9951 13910 10623 4693 1265 3853 10053 239 6039 6251 13968
internal_value=0 0.521455 0.579464 0.32886 0.341292 0.503774 0.356659 0.25087 0.368224 0.639926 0.536431 0.332979 0.585586 0.396274 0.477149 0.50754 0.327562 0.172604 0.40942 0.612785 0.677903 0.571998 0.34545 0.527497 0.401253 0.304531 0.392287 0.536248 0.431742 0.451944
internal_count=225652 131996 101442 30554 93656 45048 86943 21270 80307 56394 37345 43279 23199 13420 14004 9284 10743 10527 37028 18522 33195 23341 7703 8419 5585 29859 1504 12718 25052 18801
shrinkage=0.05



feature importances:
recent_15%_loss_kurtosis=8
all_loss_best_percentile75_ratio=4
all_loss_skew=4
recent_15%_correlation_best_percentile75_ratio=4
top_10%_loss_kurtosis=4
top_20%_loss_best_percentile75_ratio=3
all_correlation_stddev_median_ratio=2
resultFilteringMode_none=2
top_20%_loss_kurtosis=2
top_10%_loss_stddev_median_ratio=2
top_10%_loss_best_percentile75_ratio=2
top_10%_correlation_stddev_best_ratio=2
top_10%_correlation_percentile5_percentile25_ratio=2
top_10%_correlation_best_percentile50_ratio=2
recent_25_loss_stddev_best_ratio=2
recent_15%_loss_best_percentile75_ratio=2
recent_15%_loss_best_percentile25_ratio=2
recent_15%_correlation_stddev_median_ratio=2
recent_10_loss_percentile5_percentile25_ratio=2
recent_15%_loss_best_percentile50_ratio=1
log10_cardinality=1
top_30%_loss_best_percentile50_ratio=1
top_30%_loss_best_percentile75_ratio=1
all_correlation_skew=1
secondaryProbabilityMode_correlation=1
secondaryProbabilityMode_fixed=1

pandas_categorical:null
