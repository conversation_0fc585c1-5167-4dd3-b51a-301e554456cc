Metadata-Version: 2.1
Name: hyperopt
Version: 0.2.7
Summary: Distributed Asynchronous Hyperparameter Optimization
Home-page: https://hyperopt.github.io/hyperopt
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Keywords: Bayesian optimization hyperparameter model selection
Platform: Linux
Platform: OS-X
Platform: Windows
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Developers
Classifier: Environment :: Console
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Software Development
License-File: LICENSE.txt
Requires-Dist: numpy
Requires-Dist: scipy
Requires-Dist: six
Requires-Dist: networkx (>=2.2)
Requires-Dist: future
Requires-Dist: tqdm
Requires-Dist: cloudpickle
Requires-Dist: py4j
Provides-Extra: atpe
Requires-Dist: lightgbm ; extra == 'atpe'
Requires-Dist: scikit-learn ; extra == 'atpe'
Provides-Extra: mongotrials
Requires-Dist: pymongo ; extra == 'mongotrials'
Provides-Extra: sparktrials
Requires-Dist: pyspark ; extra == 'sparktrials'
Provides-Extra: dev
Requires-Dist: black ; extra == 'dev'
Requires-Dist: pre-commit ; extra == 'dev'
Requires-Dist: nose ; extra == 'dev'
Requires-Dist: pytest ; extra == 'dev'

UNKNOWN

