#!/usr/bin/env python3
"""
Test script specifically for IBM RXN API
Tests the IBM RXN API key and functionality
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ibm_rxn_direct():
    """Test IBM RXN API directly."""
    print("🔬 Testing IBM RXN API Directly")
    print("=" * 50)
    
    try:
        from rxn4chemistry import RXN4ChemistryWrapper
        
        # Get API key
        api_key = os.getenv('IBM_RXN_API_KEY')
        if not api_key:
            print("❌ IBM RXN API key not found")
            return False
        
        print(f"✅ API Key found: {api_key[:8]}...{api_key[-4:]}")
        
        # Initialize wrapper
        rxn_wrapper = RXN4ChemistryWrapper(api_key=api_key)
        print("✅ RXN4ChemistryWrapper initialized")
        
        # Test API connection by getting user info
        try:
            user_info = rxn_wrapper.current_user()
            print(f"✅ API connection successful")
            print(f"   User info: {user_info}")
        except Exception as e:
            print(f"❌ API connection failed: {e}")
            return False
        
        # List existing projects
        try:
            projects = rxn_wrapper.list_all_projects()
            print(f"✅ Projects listed successfully")
            if projects and 'projects' in projects:
                print(f"   Found {len(projects['projects'])} projects")
                for i, project in enumerate(projects['projects'][:3]):  # Show first 3
                    print(f"   Project {i+1}: {project.get('name', 'Unknown')} (ID: {project.get('id', 'Unknown')})")
                
                # Use the first project if available
                if len(projects['projects']) > 0:
                    first_project_id = projects['projects'][0].get('id')
                    if first_project_id:
                        rxn_wrapper.set_project(first_project_id)
                        print(f"✅ Using project: {first_project_id}")
                    else:
                        print("❌ No valid project ID found")
                        return False
                else:
                    # Create a new project
                    try:
                        new_project = rxn_wrapper.create_project('ChemLab_Test')
                        print(f"✅ Created new project: {rxn_wrapper.project_id}")
                    except Exception as create_error:
                        print(f"❌ Failed to create project: {create_error}")
                        return False
            else:
                print("❌ No projects found or invalid response")
                return False
                
        except Exception as e:
            print(f"❌ Failed to list projects: {e}")
            return False
        
        # Test reaction prediction
        try:
            print(f"\n🧪 Testing reaction prediction...")
            reaction_smiles = "CCO.CC(=O)O>>"  # Ethanol + Acetic acid
            print(f"   Input: {reaction_smiles}")
            
            response = rxn_wrapper.predict_reaction(reaction_smiles)
            print(f"✅ Prediction request submitted")
            print(f"   Response: {response}")
            
            if response and 'prediction_id' in response:
                prediction_id = response['prediction_id']
                print(f"   Prediction ID: {prediction_id}")
                
                # Wait for results
                import time
                max_wait = 30  # seconds
                wait_interval = 2
                elapsed = 0
                
                while elapsed < max_wait:
                    try:
                        results = rxn_wrapper.get_predict_reaction_results(prediction_id)
                        print(f"   Status check at {elapsed}s: {results.get('status', 'Unknown')}")
                        
                        if results and 'response' in results:
                            payload = results['response'].get('payload', {})
                            if payload and 'attempts' in payload:
                                attempts = payload['attempts']
                                print(f"✅ Prediction completed with {len(attempts)} attempts")
                                
                                for i, attempt in enumerate(attempts[:3]):
                                    if 'smiles' in attempt:
                                        print(f"   Result {i+1}: {attempt['smiles']}")
                                        # Extract products
                                        if '>>' in attempt['smiles']:
                                            products = attempt['smiles'].split('>>')[-1]
                                            print(f"   Products: {products}")
                                
                                return True
                        
                        if results and results.get('status') == 'RUNNING':
                            time.sleep(wait_interval)
                            elapsed += wait_interval
                        else:
                            break
                            
                    except Exception as result_error:
                        print(f"   Error getting results: {result_error}")
                        time.sleep(wait_interval)
                        elapsed += wait_interval
                
                print(f"⚠️  Prediction timed out after {elapsed} seconds")
                return False
            else:
                print(f"❌ Invalid prediction response: {response}")
                return False
                
        except Exception as e:
            print(f"❌ Prediction failed: {e}")
            return False
        
    except ImportError:
        print("❌ rxn4chemistry package not available")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_product_predictor_rxn():
    """Test IBM RXN through ProductPredictor."""
    print("\n🔧 Testing IBM RXN through ProductPredictor")
    print("=" * 50)
    
    try:
        from product_predictor import ProductPredictor
        
        predictor = ProductPredictor()
        
        if not predictor.rxn_wrapper:
            print("❌ RXN wrapper not initialized in ProductPredictor")
            return False
        
        print("✅ ProductPredictor RXN wrapper initialized")
        
        # Test prediction
        reactant_a = "CCO"  # Ethanol
        reactant_b = "CC(=O)O"  # Acetic acid
        
        print(f"   Testing: {reactant_a} + {reactant_b}")
        
        products = predictor._predict_with_rxn(reactant_a, reactant_b, 298.15, "standard")
        
        if products:
            print(f"✅ Prediction successful: {len(products)} products")
            for i, product in enumerate(products, 1):
                print(f"   Product {i}: {product}")
            return True
        else:
            print("❌ No products predicted")
            return False
            
    except Exception as e:
        print(f"❌ ProductPredictor test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 IBM RXN API Test Suite")
    print("=" * 80)
    
    # Test direct API
    direct_success = test_ibm_rxn_direct()
    
    # Test through ProductPredictor
    predictor_success = test_product_predictor_rxn()
    
    print(f"\n📊 Test Results:")
    print(f"   Direct API Test: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"   ProductPredictor Test: {'✅ PASS' if predictor_success else '❌ FAIL'}")
    
    if direct_success and predictor_success:
        print(f"\n🎉 All IBM RXN tests passed!")
    else:
        print(f"\n⚠️  Some IBM RXN tests failed.")
