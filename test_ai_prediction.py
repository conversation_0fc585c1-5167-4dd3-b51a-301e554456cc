#!/usr/bin/env python3
"""
Test script for AI-based product prediction
Tests IBM RXN and Molecular Transformer functionality
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ai_prediction():
    """Test AI prediction models."""
    print("🧪 Testing AI-based Product Prediction")
    print("=" * 60)
    
    # Import the predictor
    try:
        from product_predictor import ProductPredictor
        predictor = ProductPredictor()
    except Exception as e:
        print(f"❌ Failed to import ProductPredictor: {e}")
        return False
    
    # Test molecules
    test_cases = [
        {
            'name': 'Ethanol + Acetic Acid (Esterification)',
            'reactant_a': 'CCO',  # Ethanol
            'reactant_b': 'CC(=O)O',  # Acetic acid
            'expected_product': 'CC(=O)OCC'  # Ethyl acetate
        },
        {
            'name': 'Methanol + Formaldehyde',
            'reactant_a': 'CO',  # Methanol
            'reactant_b': 'C=O',  # Formaldehyde
            'expected_product': 'COC'  # Dimethyl ether (possible product)
        }
    ]
    
    success_count = 0
    total_tests = 0
    
    for test_case in test_cases:
        print(f"\n🔬 Test Case: {test_case['name']}")
        print(f"   Reactants: {test_case['reactant_a']} + {test_case['reactant_b']}")
        
        reactant_a = test_case['reactant_a']
        reactant_b = test_case['reactant_b']
        
        # Test IBM RXN
        print(f"\n📡 Testing IBM RXN...")
        total_tests += 1
        
        if predictor.rxn_wrapper:
            try:
                rxn_products = predictor._predict_with_rxn(reactant_a, reactant_b, 298.15, "standard")
                if rxn_products:
                    print(f"   ✅ IBM RXN Success: {len(rxn_products)} products predicted")
                    for i, product in enumerate(rxn_products, 1):
                        print(f"      Product {i}: {product}")
                    success_count += 1
                else:
                    print(f"   ⚠️  IBM RXN: No products predicted")
            except Exception as e:
                print(f"   ❌ IBM RXN Error: {e}")
        else:
            print(f"   ❌ IBM RXN: Not available (API key: {'✅' if predictor.ibm_rxn_api_key else '❌'})")
        
        # Test Molecular Transformer
        print(f"\n🤖 Testing Molecular Transformer...")
        total_tests += 1
        
        if predictor.molecular_transformer:
            try:
                transformer_products = predictor._predict_with_molecular_transformer(reactant_a, reactant_b, 298.15)
                if transformer_products:
                    print(f"   ✅ Molecular Transformer Success: {len(transformer_products)} products predicted")
                    for i, product in enumerate(transformer_products, 1):
                        print(f"      Product {i}: {product}")
                    success_count += 1
                else:
                    print(f"   ⚠️  Molecular Transformer: No products predicted")
            except Exception as e:
                print(f"   ❌ Molecular Transformer Error: {e}")
        else:
            print(f"   ❌ Molecular Transformer: Not available")
        
        # Test combined AI prediction
        print(f"\n🎯 Testing Combined AI Prediction...")
        total_tests += 1
        
        try:
            ai_products = predictor.predict_products_ai(reactant_a, reactant_b, method="auto")
            if ai_products:
                print(f"   ✅ Combined AI Success: {len(ai_products)} products predicted")
                for i, product in enumerate(ai_products, 1):
                    print(f"      Product {i}: {product}")
                success_count += 1
            else:
                print(f"   ⚠️  Combined AI: No products predicted")
        except Exception as e:
            print(f"   ❌ Combined AI Error: {e}")
        
        print("-" * 60)
    
    # Summary
    print(f"\n📊 Test Summary:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Successful: {success_count}")
    print(f"   Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    # Model availability summary
    print(f"\n🔧 Model Availability:")
    print(f"   IBM RXN API Key: {'✅' if predictor.ibm_rxn_api_key else '❌'}")
    print(f"   IBM RXN Wrapper: {'✅' if predictor.rxn_wrapper else '❌'}")
    print(f"   Molecular Transformer: {'✅' if predictor.molecular_transformer else '❌'}")
    print(f"   OpenAI API: {'✅' if predictor.ai_available else '❌'}")
    
    return success_count > 0

def test_environment_setup():
    """Test environment setup."""
    print("🔧 Testing Environment Setup")
    print("=" * 40)
    
    # Check environment variables
    env_vars = {
        'IBM_RXN_API_KEY': os.getenv('IBM_RXN_API_KEY'),
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
        'OPENROUTER_API_KEY': os.getenv('OPENROUTER_API_KEY'),
        'PERPLEXITY_API_KEY': os.getenv('PERPLEXITY_API_KEY')
    }
    
    for var_name, var_value in env_vars.items():
        if var_value:
            masked_value = var_value[:8] + "..." + var_value[-4:] if len(var_value) > 12 else "***"
            print(f"   ✅ {var_name}: {masked_value}")
        else:
            print(f"   ❌ {var_name}: Not set")
    
    # Check package availability
    packages = {
        'rxn4chemistry': 'IBM RXN4Chemistry',
        'transformers': 'Hugging Face Transformers',
        'torch': 'PyTorch',
        'rdkit': 'RDKit',
        'openai': 'OpenAI'
    }
    
    print(f"\n📦 Package Availability:")
    for package, description in packages.items():
        try:
            __import__(package)
            print(f"   ✅ {description}: Available")
        except ImportError:
            print(f"   ❌ {description}: Not available")

if __name__ == "__main__":
    print("🚀 Starting AI Prediction Tests")
    print("=" * 80)
    
    # Test environment
    test_environment_setup()
    print()
    
    # Test AI prediction
    success = test_ai_prediction()
    
    print(f"\n{'🎉 Tests completed successfully!' if success else '⚠️  Some tests failed.'}")
