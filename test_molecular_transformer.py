#!/usr/bin/env python3
"""
Test script specifically for Molecular Transformer
Tests the Molecular Transformer functionality for chemistry
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_molecular_transformer_direct():
    """Test Molecular Transformer directly."""
    print("🤖 Testing Molecular Transformer Directly")
    print("=" * 50)
    
    try:
        from transformers import AutoTokenizer, AutoModelForSeq2SeqLM, pipeline
        import torch
        
        print("✅ Transformers library available")
        
        # Test T5 model for chemistry
        model_name = "t5-small"
        print(f"📦 Loading model: {model_name}")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForSeq2SeqLM.from_pretrained(model_name)
        
        print("✅ Model loaded successfully")
        
        # Test chemistry-specific prompts
        test_cases = [
            {
                'reactants': ['CCO', 'CC(=O)O'],  # Ethanol + Acetic acid
                'prompt': 'translate English to chemistry: What happens when ethanol reacts with acetic acid?',
                'expected': 'esterification'
            },
            {
                'reactants': ['CO', 'C=O'],  # Methanol + Formaldehyde
                'prompt': 'translate English to chemistry: What are the products when methanol reacts with formaldehyde?',
                'expected': 'condensation'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test Case {i}: {' + '.join(test_case['reactants'])}")
            print(f"   Prompt: {test_case['prompt']}")
            
            # Tokenize input
            inputs = tokenizer(
                test_case['prompt'], 
                return_tensors="pt", 
                max_length=512, 
                truncation=True,
                padding=True
            )
            
            # Generate prediction
            with torch.no_grad():
                outputs = model.generate(
                    inputs.input_ids,
                    attention_mask=inputs.attention_mask,
                    max_length=128,
                    num_beams=3,
                    num_return_sequences=2,
                    temperature=0.8,
                    do_sample=True,
                    early_stopping=True,
                    pad_token_id=tokenizer.pad_token_id
                )
            
            # Decode predictions
            print(f"   Generated responses:")
            for j, output in enumerate(outputs):
                decoded = tokenizer.decode(output, skip_special_tokens=True)
                print(f"      Response {j+1}: {decoded}")
        
        return True
        
    except Exception as e:
        print(f"❌ Molecular Transformer test failed: {e}")
        import traceback
        print(f"   Error details: {traceback.format_exc()}")
        return False

def test_chemistry_specific_models():
    """Test chemistry-specific transformer models."""
    print("\n🧬 Testing Chemistry-Specific Models")
    print("=" * 50)
    
    # List of chemistry models to try
    chemistry_models = [
        "microsoft/DialoGPT-medium",  # General conversational model
        "facebook/bart-base",         # BART model
        "google/flan-t5-small",       # FLAN-T5 (instruction-tuned)
    ]
    
    success_count = 0
    
    for model_name in chemistry_models:
        print(f"\n📦 Testing model: {model_name}")
        
        try:
            from transformers import pipeline
            
            # Create a text generation pipeline
            generator = pipeline(
                "text2text-generation",
                model=model_name,
                device=0 if torch.cuda.is_available() else -1,
                max_length=100
            )
            
            print(f"✅ Model {model_name} loaded successfully")
            
            # Test chemistry prompt
            prompt = "Predict the products when ethanol (CCO) reacts with acetic acid (CC(=O)O):"
            
            results = generator(prompt, max_length=100, num_return_sequences=1)
            
            print(f"   Input: {prompt}")
            for result in results:
                print(f"   Output: {result['generated_text']}")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ Failed to load {model_name}: {e}")
    
    print(f"\n📊 Chemistry Models Summary: {success_count}/{len(chemistry_models)} models loaded successfully")
    return success_count > 0

def test_product_predictor_transformer():
    """Test Molecular Transformer through ProductPredictor."""
    print("\n🔧 Testing Molecular Transformer through ProductPredictor")
    print("=" * 50)
    
    try:
        from product_predictor import ProductPredictor
        
        predictor = ProductPredictor()
        
        if not predictor.molecular_transformer:
            print("❌ Molecular Transformer not initialized in ProductPredictor")
            return False
        
        print("✅ ProductPredictor Molecular Transformer initialized")
        print(f"   Model type: {predictor.molecular_transformer.get('type', 'Unknown')}")
        
        # Test prediction
        test_cases = [
            {
                'name': 'Ethanol + Acetic Acid',
                'reactant_a': 'CCO',
                'reactant_b': 'CC(=O)O'
            },
            {
                'name': 'Methanol + Formaldehyde',
                'reactant_a': 'CO',
                'reactant_b': 'C=O'
            }
        ]
        
        success_count = 0
        
        for test_case in test_cases:
            print(f"\n🧪 Testing: {test_case['name']}")
            print(f"   Reactants: {test_case['reactant_a']} + {test_case['reactant_b']}")
            
            try:
                products = predictor._predict_with_molecular_transformer(
                    test_case['reactant_a'], 
                    test_case['reactant_b'], 
                    298.15
                )
                
                if products:
                    print(f"✅ Prediction successful: {len(products)} products")
                    for i, product in enumerate(products, 1):
                        print(f"      Product {i}: {product}")
                    success_count += 1
                else:
                    print("⚠️  No products predicted")
                    
            except Exception as e:
                print(f"❌ Prediction failed: {e}")
        
        print(f"\n📊 Transformer Test Summary: {success_count}/{len(test_cases)} tests successful")
        return success_count > 0
            
    except Exception as e:
        print(f"❌ ProductPredictor transformer test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Molecular Transformer Test Suite")
    print("=" * 80)
    
    # Test direct transformer
    direct_success = test_molecular_transformer_direct()
    
    # Test chemistry-specific models
    chemistry_success = test_chemistry_specific_models()
    
    # Test through ProductPredictor
    predictor_success = test_product_predictor_transformer()
    
    print(f"\n📊 Final Test Results:")
    print(f"   Direct Transformer Test: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"   Chemistry Models Test: {'✅ PASS' if chemistry_success else '❌ FAIL'}")
    print(f"   ProductPredictor Test: {'✅ PASS' if predictor_success else '❌ FAIL'}")
    
    total_success = sum([direct_success, chemistry_success, predictor_success])
    
    if total_success >= 2:
        print(f"\n🎉 Molecular Transformer tests mostly successful! ({total_success}/3)")
    else:
        print(f"\n⚠️  Molecular Transformer tests need improvement. ({total_success}/3)")
