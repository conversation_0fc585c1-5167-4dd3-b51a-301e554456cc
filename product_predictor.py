"""
Product Predictor Module for ChemLab

This module predicts chemical reaction products using AI-based models and
RDKit reaction rules, then validates them using DFT and NEB calculations.

Author: ChemLab Development Team
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    logger.warning("OpenAI not available. AI prediction will be disabled.")
    OPENAI_AVAILABLE = False

try:
    from rdkit import Chem
    from rdkit.Chem import AllChem, rdChemReactions
    RDKIT_AVAILABLE = True
except ImportError:
    logger.warning("RDKit not available. Reaction rules will be limited.")
    RDKIT_AVAILABLE = False

try:
    from rxn4chemistry import RXN4ChemistryWrapper
    RXN_AVAILABLE = True
except ImportError:
    logger.warning("RXN4Chemistry not available. IBM RXN prediction will be disabled.")
    RXN_AVAILABLE = False

try:
    import chemprop
    CHEMPROP_AVAILABLE = True
except ImportError:
    logger.warning("Chemprop not available. Chemprop prediction will be disabled.")
    CHEMPROP_AVAILABLE = False

try:
    from transformers import AutoTokenizer, AutoModelForSeq2SeqLM, pipeline
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    logger.warning("Transformers not available. Molecular transformer prediction will be disabled.")
    TRANSFORMERS_AVAILABLE = False

# Import ChemLab modules
from input_handler import parse_molecule
from reaction_feasibility import ReactionFeasibilityAnalyzer
from reaction_pathway import ReactionPathwayFinder
from thermo_kinetics import ThermoKineticsCalculator


class ProductPredictor:
    """
    Predicts chemical reaction products using AI models and reaction rules.
    """
    
    def __init__(self):
        """Initialize the product predictor."""
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        self.perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
        self.ibm_rxn_api_key = os.getenv('IBM_RXN_API_KEY')

        # Initialize OpenAI client if available
        if OPENAI_AVAILABLE and self.openai_api_key:
            openai.api_key = self.openai_api_key
            self.ai_available = True
            logger.info("OpenAI API initialized")
        else:
            self.ai_available = False
            logger.warning("OpenAI API not available")

        # Initialize specialized chemistry models
        self.rxn_wrapper = None
        self.molecular_transformer = None
        self.chemprop_model = None

        self._initialize_chemistry_models()

        # Common reaction SMARTS patterns
        self.reaction_patterns = self._load_reaction_patterns()

    def _initialize_chemistry_models(self):
        """Initialize specialized chemistry transformer models."""
        # Initialize IBM RXN
        if RXN_AVAILABLE and self.ibm_rxn_api_key:
            try:
                self.rxn_wrapper = RXN4ChemistryWrapper(api_key=self.ibm_rxn_api_key)

                # Add rate limiting delay
                import time
                time.sleep(1)

                # Try to handle project setup with better error handling
                project_set = False

                # First, try to list existing projects
                try:
                    logger.info("Attempting to list existing IBM RXN projects...")
                    time.sleep(1)  # Rate limiting
                    projects = self.rxn_wrapper.list_all_projects()

                    if projects and 'response' in projects and 'projects' in projects['response']:
                        project_list = projects['response']['projects']
                        if len(project_list) > 0:
                            first_project = project_list[0]
                            project_id = first_project.get('id')
                            if project_id:
                                self.rxn_wrapper.set_project(project_id)
                                logger.info(f"IBM RXN4Chemistry using existing project: {project_id}")
                                project_set = True

                    if not project_set:
                        logger.info("No existing projects found, attempting to create new project...")
                        time.sleep(1)  # Rate limiting
                        project_response = self.rxn_wrapper.create_project('ChemLab_Predictions')
                        if project_response and 'response' in project_response:
                            project_id = project_response['response'].get('project_id')
                            if project_id:
                                logger.info(f"IBM RXN4Chemistry created new project: {project_id}")
                                project_set = True

                except Exception as project_error:
                    logger.warning(f"Project setup failed: {project_error}")
                    # Continue without project - some endpoints might still work

                if project_set:
                    logger.info("IBM RXN4Chemistry initialized successfully with project")
                else:
                    logger.warning("IBM RXN4Chemistry initialized without project - some features may not work")
            except Exception as e:
                logger.warning(f"Failed to initialize IBM RXN: {e}")
                self.rxn_wrapper = None
        else:
            self.rxn_wrapper = None
            if not RXN_AVAILABLE:
                logger.warning("RXN4Chemistry package not available")
            if not self.ibm_rxn_api_key:
                logger.warning("IBM RXN API key not found in environment variables")

        # Initialize Molecular Transformer
        if TRANSFORMERS_AVAILABLE:
            try:
                # Try chemistry-specific models in order of preference
                chemistry_models = [
                    # Note: Most chemistry-specific models are not publicly available
                    # We'll use general models with chemistry-specific prompting
                    "t5-small",  # General T5 model with chemistry prompts
                ]

                self.molecular_transformer = None
                for model_name in chemistry_models:
                    try:
                        logger.info(f"Attempting to load molecular transformer: {model_name}")
                        if "t5" in model_name.lower():
                            # Use T5 models for sequence-to-sequence generation
                            self.molecular_transformer = {
                                'tokenizer': AutoTokenizer.from_pretrained(model_name),
                                'model': AutoModelForSeq2SeqLM.from_pretrained(model_name),
                                'type': 'seq2seq'
                            }
                        else:
                            # Use pipeline for other models
                            self.molecular_transformer = {
                                'pipeline': pipeline(
                                    "text2text-generation",
                                    model=model_name,
                                    device=0 if torch.cuda.is_available() else -1
                                ),
                                'type': 'pipeline'
                            }
                        logger.info(f"Molecular Transformer initialized with model: {model_name}")
                        break
                    except Exception as model_error:
                        logger.warning(f"Failed to load {model_name}: {model_error}")
                        continue

                if self.molecular_transformer is None:
                    logger.warning("All molecular transformer models failed to load")

            except Exception as e:
                logger.warning(f"Failed to initialize Molecular Transformer: {e}")
                self.molecular_transformer = None

        # Initialize Chemprop (for property prediction, can be adapted for reactions)
        if CHEMPROP_AVAILABLE:
            try:
                # Chemprop is typically used for property prediction
                # We'll use it for reaction feasibility scoring
                logger.info("Chemprop available for property prediction")
                self.chemprop_model = True  # Placeholder - would need trained model
            except Exception as e:
                logger.warning(f"Failed to initialize Chemprop: {e}")
                self.chemprop_model = None

    def _load_reaction_patterns(self) -> List[Dict[str, str]]:
        """Load common reaction SMARTS patterns."""
        patterns = [
            {
                'name': 'SN2_substitution',
                'smarts': '[C:1][Cl,Br,I:2].[N,O:3]>>[C:1][*:3].[*:2]',
                'description': 'Nucleophilic substitution'
            },
            {
                'name': 'alcohol_oxidation',
                'smarts': '[C:1][OH:2]>>[C:1]=[O:2]',
                'description': 'Alcohol to carbonyl oxidation'
            },
            {
                'name': 'ester_hydrolysis',
                'smarts': '[C:1](=[O:2])[O:3][C:4].[OH2]>>[C:1](=[O:2])[OH].[OH][C:4]',
                'description': 'Ester hydrolysis'
            },
            {
                'name': 'aldol_condensation',
                'smarts': '[C:1][CH2:2][C:3]=[O:4].[C:5][CH2:6][C:7]=[O:8]>>[C:1][CH:2]([C:3]=[O:4])[C:5][CH2:6][C:7]=[O:8]',
                'description': 'Aldol condensation'
            },
            {
                'name': 'diels_alder',
                'smarts': '[C:1]=[C:2][C:3]=[C:4].[C:5]=[C:6]>>[C:1]1[C:2][C:5][C:6][C:4][C:3]1',
                'description': 'Diels-Alder cycloaddition'
            }
        ]
        return patterns
    
    def predict_products_ai(self, reactant_a: str, reactant_b: str,
                           temperature: float = 298.15,
                           conditions: str = "standard",
                           method: str = "auto") -> List[str]:
        """
        Predict reaction products using specialized chemistry AI models.

        Args:
            reactant_a: SMILES string of first reactant
            reactant_b: SMILES string of second reactant
            temperature: Reaction temperature in K
            conditions: Reaction conditions description
            method: AI method ('auto', 'rxn', 'transformer', 'openai')

        Returns:
            List of predicted product SMILES
        """
        logger.info(f"Predicting products using AI method: {method}")

        # Try specialized chemistry models first
        if method == "auto" or method == "rxn":
            if self.rxn_wrapper:
                products = self._predict_with_rxn(reactant_a, reactant_b, temperature, conditions)
                if products:
                    logger.info(f"IBM RXN predicted {len(products)} products")
                    return products

        if method == "auto" or method == "transformer":
            if self.molecular_transformer:
                products = self._predict_with_molecular_transformer(reactant_a, reactant_b, temperature)
                if products:
                    logger.info(f"Molecular Transformer predicted {len(products)} products")
                    return products

        # Fallback to OpenAI GPT
        if method == "auto" or method == "openai":
            if self.ai_available:
                products = self._predict_with_openai(reactant_a, reactant_b, temperature, conditions)
                if products:
                    logger.info(f"OpenAI GPT predicted {len(products)} products")
                    return products

        # Final fallback to rule-based
        logger.info("All AI methods failed, using rule-based fallback")
        return self.predict_products_rules(reactant_a, reactant_b)

    def _predict_with_rxn(self, reactant_a: str, reactant_b: str,
                         temperature: float, conditions: str) -> List[str]:
        """Predict products using IBM RXN4Chemistry."""
        try:
            # Format reaction for RXN
            reaction_smiles = f"{reactant_a}.{reactant_b}>>"
            logger.info(f"IBM RXN input: {reaction_smiles}")

            # Add rate limiting delay
            import time
            time.sleep(2)  # 2 second delay to avoid rate limits

            # Predict using RXN
            response = self.rxn_wrapper.predict_reaction(reaction_smiles)
            logger.debug(f"IBM RXN raw response: {response}")

            if response and 'prediction_id' in response:
                # Get the prediction results
                prediction_id = response['prediction_id']
                logger.info(f"IBM RXN prediction ID: {prediction_id}")

                # Wait for results (with timeout)
                import time
                max_wait_time = 60  # seconds
                wait_interval = 2   # seconds
                elapsed_time = 0

                while elapsed_time < max_wait_time:
                    try:
                        results = self.rxn_wrapper.get_predict_reaction_results(prediction_id)
                        logger.debug(f"IBM RXN results check: {results}")

                        if results and 'response' in results:
                            payload = results['response'].get('payload', {})
                            if payload and 'attempts' in payload:
                                attempts = payload['attempts']

                                products = []
                                for attempt in attempts[:3]:  # Take top 3 predictions
                                    if 'smiles' in attempt:
                                        # Extract products from reaction SMILES
                                        reaction_parts = attempt['smiles'].split('>>')
                                        if len(reaction_parts) > 1:
                                            product_smiles = reaction_parts[1].strip()
                                            if product_smiles and product_smiles not in products:
                                                # Validate SMILES if RDKit is available
                                                if RDKIT_AVAILABLE:
                                                    try:
                                                        mol = Chem.MolFromSmiles(product_smiles)
                                                        if mol is not None:
                                                            canonical_smiles = Chem.MolToSmiles(mol)
                                                            products.append(canonical_smiles)
                                                            logger.info(f"IBM RXN predicted product: {canonical_smiles}")
                                                    except:
                                                        products.append(product_smiles)
                                                        logger.info(f"IBM RXN predicted product (unvalidated): {product_smiles}")
                                                else:
                                                    products.append(product_smiles)
                                                    logger.info(f"IBM RXN predicted product: {product_smiles}")

                                if products:
                                    return products

                        # Check if prediction is still running
                        if results and results.get('status') == 'RUNNING':
                            logger.info(f"IBM RXN prediction still running, waiting...")
                            time.sleep(wait_interval)
                            elapsed_time += wait_interval
                        else:
                            break

                    except Exception as result_error:
                        logger.warning(f"Error getting IBM RXN results: {result_error}")
                        time.sleep(wait_interval)
                        elapsed_time += wait_interval

                logger.warning(f"IBM RXN prediction timed out or failed after {elapsed_time} seconds")

        except Exception as e:
            logger.warning(f"IBM RXN prediction failed: {e}")
            import traceback
            logger.debug(f"IBM RXN error details: {traceback.format_exc()}")

        return []

    def _predict_with_molecular_transformer(self, reactant_a: str, reactant_b: str,
                                          temperature: float) -> List[str]:
        """Predict products using Molecular Transformer."""
        try:
            # Format input for molecular transformer
            reaction_input = f"{reactant_a}.{reactant_b}>>"
            logger.info(f"Molecular Transformer input: {reaction_input}")

            if self.molecular_transformer and self.molecular_transformer.get('type') == 'seq2seq':
                # Use sequence-to-sequence model (T5-based)
                tokenizer = self.molecular_transformer['tokenizer']
                model = self.molecular_transformer['model']

                # Prepare input with chemistry-specific prompt for T5
                # T5 expects task-specific prompts
                chemistry_prompt = f"translate English to SMILES: What are the products when {reactant_a} reacts with {reactant_b}?"

                # Tokenize input
                inputs = tokenizer(
                    chemistry_prompt,
                    return_tensors="pt",
                    max_length=512,
                    truncation=True,
                    padding=True
                )

                # Generate prediction
                with torch.no_grad():
                    outputs = model.generate(
                        inputs.input_ids,
                        attention_mask=inputs.attention_mask,
                        max_length=256,
                        num_beams=5,
                        num_return_sequences=3,
                        temperature=0.8,
                        do_sample=True,
                        early_stopping=True,
                        pad_token_id=tokenizer.pad_token_id
                    )

                # Decode predictions
                products = []
                for output in outputs:
                    decoded = tokenizer.decode(output, skip_special_tokens=True)
                    logger.debug(f"Molecular Transformer raw output: {decoded}")

                    # Extract SMILES from the decoded text
                    extracted_smiles = self._extract_smiles_from_text(decoded)

                    for smiles in extracted_smiles:
                        if smiles and smiles not in products:
                            # Validate SMILES if RDKit is available
                            if RDKIT_AVAILABLE:
                                try:
                                    mol = Chem.MolFromSmiles(smiles)
                                    if mol is not None:
                                        # Canonicalize SMILES
                                        canonical_smiles = Chem.MolToSmiles(mol)
                                        if canonical_smiles not in products:
                                            products.append(canonical_smiles)
                                            logger.info(f"Molecular Transformer predicted: {canonical_smiles}")
                                except:
                                    # If SMILES validation fails, try simple patterns
                                    if self._looks_like_smiles(smiles):
                                        products.append(smiles)
                                        logger.info(f"Molecular Transformer predicted (unvalidated): {smiles}")
                            else:
                                if self._looks_like_smiles(smiles):
                                    products.append(smiles)
                                    logger.info(f"Molecular Transformer predicted: {smiles}")

                return products

            elif self.molecular_transformer and self.molecular_transformer.get('type') == 'pipeline':
                # Use pipeline model
                pipeline_model = self.molecular_transformer['pipeline']
                prompt = f"Predict the products of this chemical reaction: {reaction_input}"

                results = pipeline_model(
                    prompt,
                    max_length=200,
                    num_return_sequences=3,
                    temperature=0.8,
                    do_sample=True
                )

                products = []
                for result in results:
                    text = result['generated_text']
                    # Extract SMILES from generated text
                    extracted = self._extract_smiles_from_text(text)
                    products.extend(extracted)

                return products[:3]  # Return top 3

        except Exception as e:
            logger.warning(f"Molecular Transformer prediction failed: {e}")
            import traceback
            logger.debug(f"Molecular Transformer error details: {traceback.format_exc()}")

        return []

    def _predict_with_openai(self, reactant_a: str, reactant_b: str,
                           temperature: float, conditions: str) -> List[str]:
        """Predict products using OpenAI GPT (original method)."""
        try:
            prompt = self._construct_reaction_prompt(reactant_a, reactant_b, temperature, conditions)
            return self._query_openai(prompt)
        except Exception as e:
            logger.warning(f"OpenAI prediction failed: {e}")
            return []

    def _construct_reaction_prompt(self, reactant_a: str, reactant_b: str,
                                  temperature: float, conditions: str) -> str:
        """Construct prompt for AI model."""
        prompt = f"""
You are an expert chemist. Predict the most likely products of the following chemical reaction.

Reactants:
- Molecule A: {reactant_a} (SMILES)
- Molecule B: {reactant_b} (SMILES)

Conditions:
- Temperature: {temperature} K
- Conditions: {conditions}

Please predict the most probable reaction products and provide them as SMILES strings.
Consider:
1. Thermodynamic feasibility
2. Kinetic accessibility
3. Common reaction mechanisms
4. Selectivity and regioselectivity

Respond with a JSON object containing:
{{
    "products": ["product1_smiles", "product2_smiles", ...],
    "mechanism": "brief description of reaction mechanism",
    "confidence": 0.0-1.0,
    "notes": "any important considerations"
}}

Only return valid SMILES strings for products.
"""
        return prompt
    
    def _query_openai(self, prompt: str) -> List[str]:
        """Query OpenAI API for product prediction."""
        try:
            response = openai.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert computational chemist specializing in reaction prediction."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            content = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                result = json.loads(content)
                products = result.get('products', [])
                logger.info(f"AI predicted {len(products)} products")
                logger.info(f"Mechanism: {result.get('mechanism', 'Not specified')}")
                logger.info(f"Confidence: {result.get('confidence', 'Not specified')}")
                return products
            except json.JSONDecodeError:
                # Fallback: extract SMILES from text
                logger.warning("Could not parse JSON, extracting SMILES from text")
                return self._extract_smiles_from_text(content)
                
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return []
    
    def _extract_smiles_from_text(self, text: str) -> List[str]:
        """Extract SMILES strings from text response."""
        import re
        
        # Look for patterns that might be SMILES
        # This is a simple heuristic - could be improved
        smiles_pattern = r'[A-Za-z0-9@+\-\[\]()=#\\\/]+(?:\.[A-Za-z0-9@+\-\[\]()=#\\\/]+)*'
        potential_smiles = re.findall(smiles_pattern, text)
        
        valid_smiles = []
        for smiles in potential_smiles:
            if self._is_valid_smiles(smiles):
                valid_smiles.append(smiles)
        
        return valid_smiles[:5]  # Limit to 5 products
    
    def _is_valid_smiles(self, smiles: str) -> bool:
        """Check if a string is a valid SMILES."""
        if not RDKIT_AVAILABLE:
            return True  # Assume valid if can't check

        try:
            mol = Chem.MolFromSmiles(smiles)
            return mol is not None
        except:
            return False

    def _looks_like_smiles(self, text: str) -> bool:
        """
        Check if a string looks like a SMILES string.

        Args:
            text: String to check

        Returns:
            True if it looks like SMILES
        """
        if not text or len(text) < 2:
            return False

        # Check for common SMILES characters
        smiles_chars = set('CNOPSFClBrI()[]=#+-@0123456789')
        text_chars = set(text)

        # Should contain mostly SMILES characters
        common_chars = len(text_chars.intersection(smiles_chars))
        return common_chars / len(text_chars) > 0.7
    
    def predict_products_rules(self, reactant_a: str, reactant_b: str) -> List[str]:
        """
        Predict products using RDKit reaction rules.
        
        Args:
            reactant_a: SMILES string of first reactant
            reactant_b: SMILES string of second reactant
            
        Returns:
            List of predicted product SMILES
        """
        if not RDKIT_AVAILABLE:
            logger.error("RDKit not available for rule-based prediction")
            return []
        
        try:
            mol_a = Chem.MolFromSmiles(reactant_a)
            mol_b = Chem.MolFromSmiles(reactant_b)
            
            if mol_a is None or mol_b is None:
                logger.error("Invalid SMILES input")
                return []
            
            products = []
            
            # Try each reaction pattern
            for pattern in self.reaction_patterns:
                try:
                    reaction = rdChemReactions.ReactionFromSmarts(pattern['smarts'])
                    
                    # Try reaction with both orderings
                    for reactants in [(mol_a, mol_b), (mol_b, mol_a)]:
                        try:
                            product_sets = reaction.RunReactants(reactants)
                            
                            for product_set in product_sets:
                                for product in product_set:
                                    smiles = Chem.MolToSmiles(product)
                                    if smiles not in products:
                                        products.append(smiles)
                                        logger.info(f"Predicted product via {pattern['name']}: {smiles}")
                        except:
                            continue
                            
                except Exception as e:
                    logger.debug(f"Pattern {pattern['name']} failed: {e}")
                    continue
            
            # If no rule-based products, try simple combination heuristics
            if not products:
                products = self._heuristic_prediction(reactant_a, reactant_b)
            
            return products[:5]  # Limit to 5 products
            
        except Exception as e:
            logger.error(f"Rule-based prediction failed: {e}")
            return []
    
    def _heuristic_prediction(self, reactant_a: str, reactant_b: str) -> List[str]:
        """Simple heuristic predictions for common reaction types."""
        products = []
        
        try:
            mol_a = Chem.MolFromSmiles(reactant_a)
            mol_b = Chem.MolFromSmiles(reactant_b)
            
            # Simple addition reaction (for demonstration)
            # This is a very basic heuristic
            combined_smiles = f"{reactant_a}.{reactant_b}"
            products.append(combined_smiles)
            
            logger.info(f"Heuristic prediction: {combined_smiles}")
            
        except Exception as e:
            logger.error(f"Heuristic prediction failed: {e}")
        
        return products
    
    def validate_products_dft(self, reactants: List[str], products: List[str],
                             temperature: float = 298.15) -> Dict[str, Any]:
        """
        Validate predicted products using DFT calculations.
        
        Args:
            reactants: List of reactant SMILES
            products: List of product SMILES
            temperature: Temperature for analysis
            
        Returns:
            Validation results dictionary
        """
        logger.info("Validating products with DFT calculations")
        
        try:
            # Convert SMILES to ASE Atoms objects
            reactant_molecules = []
            for smiles in reactants:
                try:
                    mol = parse_molecule(smiles, 'smiles')
                    reactant_molecules.append(mol)
                except Exception as e:
                    logger.warning(f"Could not parse reactant {smiles}: {e}")
            
            product_molecules = []
            for smiles in products:
                try:
                    mol = parse_molecule(smiles, 'smiles')
                    product_molecules.append(mol)
                except Exception as e:
                    logger.warning(f"Could not parse product {smiles}: {e}")
            
            if not reactant_molecules or not product_molecules:
                return {'error': 'Could not parse molecules for DFT validation'}
            
            # Feasibility analysis
            analyzer = ReactionFeasibilityAnalyzer(temperature=temperature)
            feasibility = analyzer.analyze_reaction(
                reactant_molecules, product_molecules, method='thermodynamic'
            )
            
            # Thermodynamic analysis
            thermo_calc = ThermoKineticsCalculator(temperature=temperature)
            thermo_results = thermo_calc.calculate_reaction_thermodynamics(
                reactant_molecules, product_molecules
            )
            
            validation_results = {
                'feasibility': feasibility,
                'thermodynamics': thermo_results,
                'reactant_count': len(reactant_molecules),
                'product_count': len(product_molecules),
                'temperature': temperature,
                'validation_method': 'DFT'
            }
            
            logger.info(f"DFT validation completed:")
            logger.info(f"  Feasible: {feasibility.get('feasible', 'Unknown')}")
            logger.info(f"  ΔG: {thermo_results.get('delta_g', 'Unknown'):.6f} eV")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"DFT validation failed: {e}")
            return {'error': str(e)}
    
    def find_reaction_pathway(self, reactants: List[str], products: List[str]) -> Dict[str, Any]:
        """
        Find reaction pathway using NEB calculations.
        
        Args:
            reactants: List of reactant SMILES
            products: List of product SMILES
            
        Returns:
            Pathway analysis results
        """
        logger.info("Finding reaction pathway with NEB")
        
        try:
            # For simplicity, use first reactant and first product
            if not reactants or not products:
                return {'error': 'No reactants or products provided'}
            
            reactant_mol = parse_molecule(reactants[0], 'smiles')
            product_mol = parse_molecule(products[0], 'smiles')
            
            # Find pathway
            pathway_finder = ReactionPathwayFinder()
            pathway_results = pathway_finder.find_pathway(
                reactant_mol, product_mol, method='neb', n_images=5
            )
            
            logger.info(f"Pathway analysis completed:")
            logger.info(f"  Forward barrier: {pathway_results.get('forward_barrier', 'Unknown'):.6f} eV")
            logger.info(f"  Reaction energy: {pathway_results.get('reaction_energy', 'Unknown'):.6f} eV")
            
            return pathway_results
            
        except Exception as e:
            logger.error(f"Pathway analysis failed: {e}")
            return {'error': str(e)}
    
    def predict_and_validate(self, reactant_a: str, reactant_b: str,
                           temperature: float = 298.15,
                           use_ai: bool = True,
                           ai_method: str = "auto") -> Dict[str, Any]:
        """
        Complete pipeline: predict products and validate with DFT/NEB.

        Args:
            reactant_a: SMILES string of first reactant
            reactant_b: SMILES string of second reactant
            temperature: Reaction temperature
            use_ai: Whether to use AI prediction
            ai_method: AI method ('auto', 'rxn', 'transformer', 'openai')

        Returns:
            Complete analysis results
        """
        logger.info(f"Starting product prediction and validation pipeline")
        logger.info(f"Reactants: {reactant_a} + {reactant_b}")
        logger.info(f"AI method: {ai_method}")

        results = {
            'reactants': [reactant_a, reactant_b],
            'temperature': temperature,
            'ai_method': ai_method,
            'timestamp': None
        }

        # Predict products
        if use_ai:
            products = self.predict_products_ai(reactant_a, reactant_b, temperature, method=ai_method)
            results['prediction_method'] = f'AI-{ai_method}'
        else:
            products = self.predict_products_rules(reactant_a, reactant_b)
            results['prediction_method'] = 'Rules'

        results['predicted_products'] = products
        
        if not products:
            results['error'] = 'No products predicted'
            return results
        
        # Validate with DFT
        validation = self.validate_products_dft([reactant_a, reactant_b], products, temperature)
        results['dft_validation'] = validation
        
        # Find pathway for most promising product
        if products and not validation.get('error'):
            pathway = self.find_reaction_pathway([reactant_a, reactant_b], [products[0]])
            results['pathway_analysis'] = pathway
        
        # Summary
        if 'feasibility' in validation:
            results['summary'] = {
                'feasible': validation['feasibility'].get('feasible', False),
                'confidence': validation['feasibility'].get('confidence', 0.0),
                'delta_g': validation.get('thermodynamics', {}).get('delta_g', None),
                'activation_barrier': results.get('pathway_analysis', {}).get('forward_barrier', None)
            }
        
        logger.info("Pipeline completed successfully")
        return results


# Convenience functions
def predict_reaction_products(reactant_a: str, reactant_b: str,
                            temperature: float = 298.15,
                            use_ai: bool = True,
                            ai_method: str = "auto") -> Dict[str, Any]:
    """
    Quick function to predict and validate reaction products.

    Args:
        reactant_a: SMILES string of first reactant
        reactant_b: SMILES string of second reactant
        temperature: Reaction temperature
        use_ai: Whether to use AI prediction
        ai_method: AI method ('auto', 'rxn', 'transformer', 'openai')

    Returns:
        Complete analysis results
    """
    predictor = ProductPredictor()
    return predictor.predict_and_validate(reactant_a, reactant_b, temperature, use_ai, ai_method)


if __name__ == "__main__":
    # Example usage
    try:
        print("🧪 Testing Product Predictor")
        print("=" * 50)
        
        # Test with simple molecules
        reactant_a = "CCO"  # Ethanol
        reactant_b = "CC(=O)O"  # Acetic acid
        
        predictor = ProductPredictor()
        
        # Test rule-based prediction
        print(f"Testing rule-based prediction:")
        print(f"Reactants: {reactant_a} + {reactant_b}")
        
        products_rules = predictor.predict_products_rules(reactant_a, reactant_b)
        print(f"Rule-based products: {products_rules}")
        
        # Test AI prediction (if available)
        if predictor.ai_available:
            print(f"\nTesting AI prediction:")
            products_ai = predictor.predict_products_ai(reactant_a, reactant_b)
            print(f"AI products: {products_ai}")
        
        # Test full pipeline
        print(f"\nTesting full pipeline:")
        results = predictor.predict_and_validate(reactant_a, reactant_b, use_ai=False)
        
        print(f"Predicted products: {results.get('predicted_products', [])}")
        if 'summary' in results:
            summary = results['summary']
            print(f"Feasible: {summary.get('feasible', 'Unknown')}")
            print(f"ΔG: {summary.get('delta_g', 'Unknown')} eV")
        
        print("✅ Product predictor tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
